{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🗄️ Vector Database Setup - Production-Scale Storage\n", "\n", "## 🤔 Why Do We Need Production Vector Databases?\n", "\n", "**FAISS Limitations (What We Used Before):**\n", "- **Local only**: Can't share across applications\n", "- **No persistence**: Data lost when program stops\n", "- **No concurrent access**: One user at a time\n", "- **No metadata filtering**: Limited search capabilities\n", "\n", "**Production Vector Database Benefits:**\n", "- **Scalable**: Handle millions/billions of vectors\n", "- **Persistent**: Data survives restarts\n", "- **Concurrent**: Multiple users simultaneously\n", "- **Rich queries**: Metadata filtering, hybrid search\n", "- **Managed**: Automatic backups, monitoring\n", "\n", "## 🏆 Vector Database Options\n", "\n", "### 🍃 **MongoDB Atlas Vector Search**\n", "- **What**: MongoDB with vector search capabilities\n", "- **Pros**: Familiar MongoDB interface, rich metadata queries\n", "- **Cons**: Newer vector features\n", "- **Best For**: Apps already using MongoDB\n", "\n", "### ⭐ **AstraDB (DataStax)**\n", "- **What**: Cassandra-based vector database\n", "- **Pros**: High performance, good free tier\n", "- **Cons**: Learning curve for <PERSON>\n", "- **Best For**: High-performance applications\n", "\n", "### 🔍 **OpenSearch**\n", "- **What**: Elasticsearch fork with vector search\n", "- **Pros**: Excellent text + vector hybrid search\n", "- **Cons**: Complex setup\n", "- **Best For**: Text-heavy applications\n", "\n", "### 🚀 **<PERSON><PERSON><PERSON><PERSON>**\n", "- **What**: Purpose-built vector database\n", "- **Pros**: Fastest vector operations, best indexing\n", "- **Cons**: Vector-only (limited metadata)\n", "- **Best For**: Pure vector similarity search\n", "\n", "## 🎯 What We'll Build\n", "We'll implement **one** vector database with **all three indexing methods** for comparison!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🛠️ Step 1.1: Import Vector Database Libraries\n", "# We'll import all options and choose one based on availability\n", "\n", "import os\n", "import numpy as np\n", "import pandas as pd\n", "from pathlib import Path\n", "import json\n", "import time\n", "from typing import List, Dict, Any, Optional\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Load environment variables\n", "from dotenv import load_dotenv\n", "load_dotenv()\n", "\n", "# Embeddings\n", "from langchain_huggingface import HuggingFaceEmbeddings\n", "\n", "print(\"✅ Core libraries imported!\")\n", "print(\"🎯 Next: We'll check which vector databases are available\")\n", "\n", "# Check available vector database options\n", "available_dbs = []\n", "\n", "# MongoDB Atlas\n", "try:\n", "    import pymongo\n", "    from pymongo import MongoClient\n", "    available_dbs.append(\"MongoDB Atlas\")\n", "    print(\"✅ MongoDB Atlas client available\")\n", "except ImportError:\n", "    print(\"❌ MongoDB Atlas not available (pip install pymongo)\")\n", "\n", "# AstraDB\n", "try:\n", "    from astrapy import DataAPIClient\n", "    available_dbs.append(\"AstraDB\")\n", "    print(\"✅ AstraDB client available\")\n", "except ImportError:\n", "    print(\"❌ AstraDB not available (pip install astrapy)\")\n", "\n", "# OpenSearch\n", "try:\n", "    from opensearchpy import OpenSearch\n", "    available_dbs.append(\"OpenSearch\")\n", "    print(\"✅ OpenSearch client available\")\n", "except ImportError:\n", "    print(\"❌ OpenSearch not available (pip install opensearch-py)\")\n", "\n", "# Milvus\n", "try:\n", "    from pymilvus import connections, Collection, FieldSchema, CollectionSchema, DataType\n", "    available_dbs.append(\"Milvu<PERSON>\")\n", "    print(\"✅ Milvus client available\")\n", "except ImportError:\n", "    print(\"❌ Milvus not available (pip install pymilvus)\")\n", "\n", "print(f\"\\n📊 Available vector databases: {available_dbs}\")\n", "if not available_dbs:\n", "    print(\"⚠️ No vector databases available. Please install at least one.\")\n", "    print(\"💡 Recommended: pip install pymongo astrapy opensearch-py pymilvus\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🎯 Step 1.2: <PERSON>ose Vector Database\n", "# Let's select which database to use based on availability and preference\n", "\n", "# Database selection priority (you can change this)\n", "preference_order = [\"Milvus\", \"AstraDB\", \"MongoDB Atlas\", \"OpenSearch\"]\n", "\n", "selected_db = None\n", "for db in preference_order:\n", "    if db in available_dbs:\n", "        selected_db = db\n", "        break\n", "\n", "if selected_db:\n", "    print(f\"🎯 Selected vector database: {selected_db}\")\n", "    print(f\"\\n💡 Why {selected_db}:\")\n", "    \n", "    if selected_db == \"Milvus\":\n", "        print(\"   • Purpose-built for vector search\")\n", "        print(\"   • Excellent performance and indexing options\")\n", "        print(\"   • Supports all three index types we need\")\n", "    elif selected_db == \"AstraDB\":\n", "        print(\"   • High-performance Cassandra backend\")\n", "        print(\"   • Good free tier for learning\")\n", "        print(\"   • Excellent for production workloads\")\n", "    elif selected_db == \"MongoDB Atlas\":\n", "        print(\"   • Familiar MongoDB interface\")\n", "        print(\"   • Rich metadata querying\")\n", "        print(\"   • Good integration with existing apps\")\n", "    elif selected_db == \"OpenSearch\":\n", "        print(\"   • Excellent hybrid text + vector search\")\n", "        print(\"   • Great for text-heavy applications\")\n", "        print(\"   • Powerful query capabilities\")\n", "        \n", "else:\n", "    print(\"❌ No vector database available!\")\n", "    print(\"💡 Please install at least one vector database client\")\n", "    selected_db = \"None\"\n", "\n", "print(f\"\\n🚀 Proceeding with: {selected_db}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔧 Step 2: Database Connection and Setup\n", "\n", "### 🎯 What We Need to Connect:\n", "Each vector database requires different connection parameters:\n", "\n", "#### 🍃 **MongoDB Atlas**:\n", "- **Connection string**: MongoDB URI\n", "- **Database name**: Your database\n", "- **Collection name**: Where vectors are stored\n", "\n", "#### ⭐ **AstraDB**:\n", "- **API endpoint**: Your database URL\n", "- **Application token**: Authentication\n", "- **Keyspace**: Database namespace\n", "\n", "#### 🔍 **OpenSearch**:\n", "- **Host**: OpenSearch cluster endpoint\n", "- **Port**: Usually 9200\n", "- **Authentication**: Username/password or API key\n", "\n", "#### 🚀 **Mi<PERSON><PERSON><PERSON>**:\n", "- **Host**: Milvus server address\n", "- **Port**: Usually 19530\n", "- **Collection name**: Where vectors are stored\n", "\n", "### 🔑 Environment Variables Needed:\n", "Add these to your `.env` file based on your chosen database!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🔑 Step 2.1: Check Database Credentials\n", "# Verify we have the necessary connection information\n", "\n", "def check_credentials(db_name: str) -> bool:\n", "    \"\"\"Check if we have the required credentials for the selected database\"\"\"\n", "    \n", "    if db_name == \"MongoDB Atlas\":\n", "        required_vars = [\"MONGODB_URI\"]\n", "        optional_vars = [\"MONGODB_DB_NAME\"]\n", "        \n", "    elif db_name == \"AstraDB\":\n", "        required_vars = [\"ASTRA_DB_API_ENDPOINT\", \"ASTRA_DB_APPLICATION_TOKEN\"]\n", "        optional_vars = [\"ASTRA_DB_KEYSPACE\"]\n", "        \n", "    elif db_name == \"OpenSearch\":\n", "        required_vars = [\"OPENSEARCH_HOST\"]\n", "        optional_vars = [\"OPENSEARCH_PORT\", \"OPENSEARCH_USERNAME\", \"OPENSEARCH_PASSWORD\"]\n", "        \n", "    elif db_name == \"Milvu<PERSON>\":\n", "        required_vars = [\"MILVUS_HOST\"]\n", "        optional_vars = [\"MILVUS_PORT\"]\n", "        \n", "    else:\n", "        return False\n", "    \n", "    print(f\"🔑 Checking credentials for {db_name}:\")\n", "    \n", "    # Check required variables\n", "    missing_required = []\n", "    for var in required_vars:\n", "        if os.getenv(var):\n", "            print(f\"   ✅ {var}: Set\")\n", "        else:\n", "            print(f\"   ❌ {var}: Missing\")\n", "            missing_required.append(var)\n", "    \n", "    # Check optional variables\n", "    for var in optional_vars:\n", "        if os.getenv(var):\n", "            print(f\"   ✅ {var}: Set\")\n", "        else:\n", "            print(f\"   ⚠️ {var}: Not set (using default)\")\n", "    \n", "    if missing_required:\n", "        print(f\"\\n❌ Missing required credentials: {missing_required}\")\n", "        print(\"💡 Add these to your .env file:\")\n", "        for var in missing_required:\n", "            print(f\"   {var}=your_value_here\")\n", "        return False\n", "    else:\n", "        print(f\"\\n✅ All required credentials available for {db_name}!\")\n", "        return True\n", "\n", "# Check credentials for selected database\n", "if selected_db != \"None\":\n", "    credentials_ok = check_credentials(selected_db)\n", "else:\n", "    credentials_ok = False\n", "    print(\"⚠️ No database selected - will use local FAISS as fallback\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🏗️ Step 2.2: Initialize Embedding Model\n", "# We need consistent embeddings for all our vector operations\n", "\n", "print(\"🤖 Initializing embedding model...\")\n", "\n", "# Initialize embeddings model\n", "embeddings_model = HuggingFaceEmbeddings(\n", "    model_name=\"all-MiniLM-L6-v2\",\n", "    model_kwargs={'device': 'cpu'},\n", "    encode_kwargs={'normalize_embeddings': True}\n", ")\n", "\n", "# Test the model\n", "test_text = \"This is a test document for vector embedding.\"\n", "test_embedding = embeddings_model.embed_query(test_text)\n", "\n", "print(f\"✅ Embedding model ready!\")\n", "print(f\"📏 Embedding dimension: {len(test_embedding)}\")\n", "print(f\"🧪 Test embedding (first 5 values): {test_embedding[:5]}\")\n", "\n", "# Store embedding dimension for database setup\n", "EMBEDDING_DIM = len(test_embedding)\n", "\n", "print(f\"\\n🎯 Using {EMBEDDING_DIM}-dimensional embeddings\")\n", "print(\"💡 This dimension must match across all database operations\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🗄️ Step 3: Database-Specific Implementation\n", "\n", "### 🎯 What We're Building:\n", "A **unified vector database interface** that works with any of our supported databases.\n", "\n", "### 🔧 Key Operations:\n", "1. **Connection**: Establish database connection\n", "2. **Schema Creation**: Set up collections/indexes\n", "3. **Data Insertion**: Store vectors with metadata\n", "4. **Search**: Query for similar vectors\n", "5. **Index Management**: Create different index types\n", "\n", "### 📊 Index Types We'll Implement:\n", "- **Flat (Brute Force)**: Exact search, baseline performance\n", "- **HNSW**: Hierarchical Navigable Small World (fast approximate)\n", "- **IVF**: Inverted File (memory efficient)\n", "\n", "Each index type has different **speed vs accuracy** trade-offs!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🏗️ Step 3.1: Vector Database Interface Class\n", "# Create a unified interface that works with any vector database\n", "\n", "class VectorDatabaseInterface:\n", "    \"\"\"\n", "    Unified interface for different vector databases\n", "    \"\"\"\n", "    \n", "    def __init__(self, db_type: str, embedding_dim: int):\n", "        self.db_type = db_type\n", "        self.embedding_dim = embedding_dim\n", "        self.client = None\n", "        self.collection = None\n", "        self.connected = False\n", "        \n", "        print(f\"🏗️ Initializing {db_type} interface\")\n", "        print(f\"📏 Embedding dimension: {embedding_dim}\")\n", "    \n", "    def connect(self) -> bool:\n", "        \"\"\"\n", "        Establish connection to the vector database\n", "        \"\"\"\n", "        try:\n", "            if self.db_type == \"Milvus\":\n", "                return self._connect_milvus()\n", "            elif self.db_type == \"AstraDB\":\n", "                return self._connect_astradb()\n", "            elif self.db_type == \"MongoDB Atlas\":\n", "                return self._connect_mongodb()\n", "            elif self.db_type == \"OpenSearch\":\n", "                return self._connect_opensearch()\n", "            else:\n", "                print(f\"❌ Unsupported database type: {self.db_type}\")\n", "                return False\n", "                \n", "        except Exception as e:\n", "            print(f\"❌ Connection failed: {e}\")\n", "            return False\n", "    \n", "    def _connect_milvus(self) -> bool:\n", "        \"\"\"\n", "        Connect to Milvus database\n", "        \"\"\"\n", "        host = os.getenv(\"MILVUS_HOST\", \"localhost\")\n", "        port = os.getenv(\"MILVUS_PORT\", \"19530\")\n", "        \n", "        print(f\"🚀 Connecting to Milvus at {host}:{port}...\")\n", "        \n", "        try:\n", "            connections.connect(\"default\", host=host, port=port)\n", "            print(\"✅ Connected to Mil<PERSON><PERSON>!\")\n", "            self.connected = True\n", "            return True\n", "        except Exception as e:\n", "            print(f\"❌ <PERSON><PERSON><PERSON><PERSON> connection failed: {e}\")\n", "            print(\"💡 Make sure <PERSON><PERSON><PERSON><PERSON> is running:\")\n", "            print(\"   docker run -p 19530:19530 milvusdb/milvus\")\n", "            return False\n", "    \n", "    def _connect_astradb(self) -> bool:\n", "        \"\"\"\n", "        Connect to AstraDB\n", "        \"\"\"\n", "        endpoint = os.getenv(\"ASTRA_DB_API_ENDPOINT\")\n", "        token = os.getenv(\"ASTRA_DB_APPLICATION_TOKEN\")\n", "        \n", "        print(f\"⭐ Connecting to AstraDB...\")\n", "        \n", "        try:\n", "            self.client = DataAPIClient(token)\n", "            self.database = self.client.get_database_by_api_endpoint(endpoint)\n", "            print(\"✅ Connected to AstraDB!\")\n", "            self.connected = True\n", "            return True\n", "        except Exception as e:\n", "            print(f\"❌ AstraDB connection failed: {e}\")\n", "            print(\"💡 Check your API endpoint and token\")\n", "            return False\n", "    \n", "    def _connect_mongodb(self) -> bool:\n", "        \"\"\"\n", "        Connect to MongoDB Atlas\n", "        \"\"\"\n", "        uri = os.getenv(\"MONGODB_URI\")\n", "        \n", "        print(f\"🍃 Connecting to MongoDB Atlas...\")\n", "        \n", "        try:\n", "            self.client = MongoClient(uri)\n", "            # Test connection\n", "            self.client.admin.command('ping')\n", "            print(\"✅ Connected to MongoDB Atlas!\")\n", "            self.connected = True\n", "            return True\n", "        except Exception as e:\n", "            print(f\"❌ MongoDB connection failed: {e}\")\n", "            print(\"💡 Check your connection string\")\n", "            return False\n", "    \n", "    def _connect_opensearch(self) -> bool:\n", "        \"\"\"\n", "        Connect to OpenSearch\n", "        \"\"\"\n", "        host = os.getenv(\"OPENSEARCH_HOST\", \"localhost\")\n", "        port = int(os.getenv(\"OPENSEARCH_PORT\", \"9200\"))\n", "        \n", "        print(f\"🔍 Connecting to OpenSearch at {host}:{port}...\")\n", "        \n", "        try:\n", "            self.client = OpenSearch(\n", "                hosts=[{'host': host, 'port': port}],\n", "                http_compress=True,\n", "                use_ssl=False,\n", "                verify_certs=False\n", "            )\n", "            # Test connection\n", "            info = self.client.info()\n", "            print(f\"✅ Connected to OpenSearch {info['version']['number']}!\")\n", "            self.connected = True\n", "            return True\n", "        except Exception as e:\n", "            print(f\"❌ OpenSearch connection failed: {e}\")\n", "            print(\"💡 Make sure OpenSearch is running\")\n", "            return False\n", "\n", "print(\"✅ Vector database interface class created!\")\n", "print(\"🎯 Supports: Milvus, AstraDB, MongoDB Atlas, OpenSearch\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 4}