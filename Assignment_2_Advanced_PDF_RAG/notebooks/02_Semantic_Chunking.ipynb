{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🔪 Semantic Chunking - Intelligent Document Splitting\n", "\n", "## 🤔 What Problem Are We Solving?\n", "\n", "**Traditional Chunking Problems:**\n", "- **Fixed-size chunks**: Cut sentences in the middle\n", "- **No context awareness**: Related information gets separated\n", "- **Poor retrieval**: Chunks don't represent complete thoughts\n", "- **Lost meaning**: Important connections are broken\n", "\n", "**Semantic Chunking Solution:**\n", "- **Meaning-based splitting**: Keep related content together\n", "- **Context preservation**: Maintain logical document flow\n", "- **Better retrieval**: Each chunk represents a complete concept\n", "- **Adaptive sizing**: Chunks vary based on content complexity\n", "\n", "## 🧠 Semantic Chunking Techniques\n", "\n", "### 1. **Sentence-Level Chunking**\n", "- **What**: Split at sentence boundaries, group related sentences\n", "- **Why**: Preserves complete thoughts\n", "- **When**: General text documents\n", "\n", "### 2. **Paragraph-Level Chunking**\n", "- **What**: Use paragraph breaks as natural boundaries\n", "- **Why**: Paragraphs usually contain related ideas\n", "- **When**: Well-structured documents\n", "\n", "### 3. **Section-Level Chunking**\n", "- **What**: Split by headers and document structure\n", "- **Why**: Maintains document hierarchy\n", "- **When**: Academic papers, reports\n", "\n", "### 4. **Embedding-Based Chunking**\n", "- **What**: Use AI to detect topic changes\n", "- **Why**: Most intelligent splitting method\n", "- **When**: Complex, unstructured documents\n", "\n", "## 🎯 What We'll Implement\n", "We'll build a **hybrid semantic chunker** that combines multiple techniques for optimal results!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🛠️ Step 1.1: Import Semantic Chunking Libraries\n", "# Each library serves a specific purpose in intelligent document splitting\n", "\n", "import numpy as np\n", "import pandas as pd\n", "from pathlib import Path\n", "import json\n", "import re\n", "from typing import List, Dict, Any, Tuple\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# <PERSON><PERSON><PERSON>n text splitters\n", "from langchain_text_splitters import (\n", "    RecursiveCharacterTextSplitter,\n", "    CharacterTextSplitter,\n", "    TokenTextSplitter\n", ")\n", "\n", "# Sentence processing\n", "import nltk\n", "from nltk.tokenize import sent_tokenize, word_tokenize\n", "from nltk.corpus import stopwords\n", "\n", "# Embeddings for semantic similarity\n", "from langchain_huggingface import HuggingFaceEmbeddings\n", "from sklearn.metrics.pairwise import cosine_similarity\n", "from sklearn.cluster import KMeans\n", "\n", "print(\"✅ Semantic chunking libraries imported!\")\n", "print(\"🎯 What each tool does:\")\n", "print(\"   • Lang<PERSON><PERSON>n splitters: Various chunking strategies\")\n", "print(\"   • NLTK: Sentence and word tokenization\")\n", "print(\"   • HuggingFace: Embeddings for semantic analysis\")\n", "print(\"   • Scikit-learn: Clustering and similarity metrics\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 📥 Step 1.2: Download Required NLTK Data\n", "# NLTK needs specific datasets for sentence tokenization\n", "\n", "try:\n", "    # Download required NLTK data\n", "    nltk.download('punkt', quiet=True)\n", "    nltk.download('stopwords', quiet=True)\n", "    print(\"✅ NLTK data downloaded successfully!\")\n", "    \n", "    # Test sentence tokenization\n", "    test_text = \"This is a test sentence. This is another sentence!\"\n", "    sentences = sent_tokenize(test_text)\n", "    print(f\"🧪 Test tokenization: {len(sentences)} sentences detected\")\n", "    \n", "except Exception as e:\n", "    print(f\"⚠️ NLTK setup issue: {e}\")\n", "    print(\"💡 Will use basic splitting as fallback\")\n", "\n", "print(\"\\n🎯 Why we need NLTK:\")\n", "print(\"   • Accurate sentence boundary detection\")\n", "print(\"   • Handles abbreviations and edge cases\")\n", "print(\"   • Better than simple period splitting\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🤖 Step 1.3: Initialize Embedding Model for Semantic Analysis\n", "# We need embeddings to understand semantic similarity between text segments\n", "\n", "print(\"🤖 Loading embedding model for semantic analysis...\")\n", "\n", "# Initialize embeddings model\n", "embeddings_model = HuggingFaceEmbeddings(\n", "    model_name=\"all-MiniLM-L6-v2\",\n", "    model_kwargs={'device': 'cpu'},\n", "    encode_kwargs={'normalize_embeddings': True}\n", ")\n", "\n", "print(\"✅ Embedding model loaded!\")\n", "print(\"🎯 What this enables:\")\n", "print(\"   • Measure semantic similarity between sentences\")\n", "print(\"   • Detect topic changes in documents\")\n", "print(\"   • Group related content together\")\n", "print(\"   • Create more intelligent chunk boundaries\")\n", "\n", "# Test the model\n", "test_sentences = [\n", "    \"Machine learning is a subset of artificial intelligence.\",\n", "    \"AI algorithms can process large amounts of data.\",\n", "    \"The weather today is sunny and warm.\"\n", "]\n", "\n", "print(\"\\n🧪 Testing semantic similarity:\")\n", "embeddings = [embeddings_model.embed_query(sent) for sent in test_sentences]\n", "sim_1_2 = cosine_similarity([embeddings[0]], [embeddings[1]])[0][0]\n", "sim_1_3 = cosine_similarity([embeddings[0]], [embeddings[2]])[0][0]\n", "\n", "print(f\"   ML vs AI similarity: {sim_1_2:.3f} (should be high)\")\n", "print(f\"   ML vs Weather similarity: {sim_1_3:.3f} (should be low)\")\n", "print(\"✅ Semantic analysis working correctly!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔧 Step 2: Building the Semantic Chunker\n", "\n", "### 🎯 Our Hybrid Approach:\n", "We'll create a **multi-strategy semantic chunker** that:\n", "\n", "1. **Structure-Aware**: Respects document hierarchy (headers, sections)\n", "2. **Sentence-Boundary**: Never cuts sentences in the middle\n", "3. **Semantic-Coherent**: Groups semantically related content\n", "4. **Size-Optimized**: Maintains reasonable chunk sizes for retrieval\n", "\n", "### 🔄 The Process:\n", "```\n", "Document → Structure Detection → Sentence Splitting → Semantic Grouping → Optimized Chunks\n", "```\n", "\n", "### 💡 Why This Works Better:\n", "- **Better retrieval**: Each chunk represents a complete concept\n", "- **Preserved context**: Related information stays together\n", "- **Flexible sizing**: Adapts to content complexity\n", "- **Structure awareness**: Maintains document organization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🏗️ Step 2.1: Semantic Chunker Class Definition\n", "# This is our main semantic chunking engine\n", "\n", "class SemanticChunker:\n", "    \"\"\"\n", "    Advanced semantic chunker that preserves meaning and context\n", "    \"\"\"\n", "    \n", "    def __init__(self, \n", "                 embeddings_model,\n", "                 max_chunk_size: int = 1000,\n", "                 min_chunk_size: int = 100,\n", "                 similarity_threshold: float = 0.7,\n", "                 overlap_size: int = 50):\n", "        \"\"\"\n", "        Initialize the semantic chunker\n", "        \n", "        Args:\n", "            embeddings_model: Model for generating embeddings\n", "            max_chunk_size: Maximum characters per chunk\n", "            min_chunk_size: Minimum characters per chunk\n", "            similarity_threshold: <PERSON><PERSON><PERSON><PERSON> for grouping similar sentences\n", "            overlap_size: Characters to overlap between chunks\n", "        \"\"\"\n", "        self.embeddings_model = embeddings_model\n", "        self.max_chunk_size = max_chunk_size\n", "        self.min_chunk_size = min_chunk_size\n", "        self.similarity_threshold = similarity_threshold\n", "        self.overlap_size = overlap_size\n", "        \n", "        print(f\"🔧 Semantic <PERSON> initialized:\")\n", "        print(f\"   📏 Max chunk size: {max_chunk_size} characters\")\n", "        print(f\"   📏 Min chunk size: {min_chunk_size} characters\")\n", "        print(f\"   🎯 Similarity threshold: {similarity_threshold}\")\n", "        print(f\"   🔄 Overlap size: {overlap_size} characters\")\n", "    \n", "    def detect_structure(self, text: str) -> List[Dict[str, Any]]:\n", "        \"\"\"\n", "        Detect document structure (headers, sections, etc.)\n", "        \"\"\"\n", "        sections = []\n", "        lines = text.split('\\n')\n", "        \n", "        current_section = {\n", "            'type': 'content',\n", "            'level': 0,\n", "            'title': '',\n", "            'content': '',\n", "            'start_line': 0\n", "        }\n", "        \n", "        for i, line in enumerate(lines):\n", "            line = line.strip()\n", "            \n", "            # Detect headers (simple heuristics)\n", "            if self._is_header(line):\n", "                # Save previous section\n", "                if current_section['content'].strip():\n", "                    sections.append(current_section.copy())\n", "                \n", "                # Start new section\n", "                current_section = {\n", "                    'type': 'header',\n", "                    'level': self._get_header_level(line),\n", "                    'title': line,\n", "                    'content': line + '\\n',\n", "                    'start_line': i\n", "                }\n", "            else:\n", "                current_section['content'] += line + '\\n'\n", "        \n", "        # Add final section\n", "        if current_section['content'].strip():\n", "            sections.append(current_section)\n", "        \n", "        return sections\n", "    \n", "    def _is_header(self, line: str) -> bool:\n", "        \"\"\"\n", "        Simple heuristics to detect headers\n", "        \"\"\"\n", "        if not line.strip():\n", "            return False\n", "        \n", "        # Check for common header patterns\n", "        header_patterns = [\n", "            r'^\\d+\\.\\s+',  # \"1. Introduction\"\n", "            r'^[A-Z][A-Z\\s]+$',  # \"INTRODUCTION\"\n", "            r'^#{1,6}\\s+',  # Markdown headers\n", "            r'^[A-Z][a-z]+:$',  # \"Introduction:\"\n", "        ]\n", "        \n", "        for pattern in header_patterns:\n", "            if re.match(pattern, line):\n", "                return True\n", "        \n", "        # Short lines that are all caps or title case\n", "        if len(line) < 100 and (line.isupper() or line.istitle()):\n", "            return True\n", "        \n", "        return False\n", "    \n", "    def _get_header_level(self, line: str) -> int:\n", "        \"\"\"\n", "        Determine header level (1-6)\n", "        \"\"\"\n", "        if line.startswith('#'):\n", "            return min(line.count('#'), 6)\n", "        elif re.match(r'^\\d+\\.', line):\n", "            return 1\n", "        elif line.isupper():\n", "            return 1\n", "        else:\n", "            return 2\n", "\n", "print(\"✅ Semantic Chunker class defined!\")\n", "print(\"🎯 Key features:\")\n", "print(\"   • Structure detection for headers and sections\")\n", "print(\"   • Configurable chunk sizes and similarity thresholds\")\n", "print(\"   • Intelligent boundary detection\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🧠 Step 2.2: Semantic Similarity Methods\n", "# Add methods for semantic analysis to our chunker\n", "\n", "class SemanticChunker(SemanticChunker):  # Extend the existing class\n", "    \n", "    def split_into_sentences(self, text: str) -> List[str]:\n", "        \"\"\"\n", "        Split text into sentences using NLTK\n", "        \"\"\"\n", "        try:\n", "            sentences = sent_tokenize(text)\n", "            # Clean up sentences\n", "            cleaned_sentences = []\n", "            for sent in sentences:\n", "                sent = sent.strip()\n", "                if sent and len(sent) > 10:  # Filter out very short sentences\n", "                    cleaned_sentences.append(sent)\n", "            return cleaned_sentences\n", "        except:\n", "            # Fallback to simple splitting\n", "            sentences = re.split(r'[.!?]+', text)\n", "            return [s.strip() for s in sentences if s.strip() and len(s.strip()) > 10]\n", "    \n", "    def calculate_sentence_similarities(self, sentences: List[str]) -> np.ndarray:\n", "        \"\"\"\n", "        Calculate similarity matrix between all sentences\n", "        \"\"\"\n", "        if len(sentences) < 2:\n", "            return np.array([[1.0]])\n", "        \n", "        print(f\"🧮 Calculating similarities for {len(sentences)} sentences...\")\n", "        \n", "        # Generate embeddings for all sentences\n", "        embeddings = []\n", "        for i, sentence in enumerate(sentences):\n", "            if i % 10 == 0:  # Progress indicator\n", "                print(f\"   Processing sentence {i+1}/{len(sentences)}\")\n", "            \n", "            embedding = self.embeddings_model.embed_query(sentence)\n", "            embeddings.append(embedding)\n", "        \n", "        # Calculate similarity matrix\n", "        similarity_matrix = cosine_similarity(embeddings)\n", "        \n", "        print(f\"✅ Similarity matrix calculated: {similarity_matrix.shape}\")\n", "        return similarity_matrix\n", "    \n", "    def find_topic_boundaries(self, sentences: List[str], similarity_matrix: np.n<PERSON><PERSON>) -> List[int]:\n", "        \"\"\"\n", "        Find boundaries where topics change based on semantic similarity\n", "        \"\"\"\n", "        if len(sentences) < 3:\n", "            return [0, len(sentences)]\n", "        \n", "        boundaries = [0]  # Always start with first sentence\n", "        \n", "        # Look for significant drops in similarity\n", "        for i in range(1, len(sentences) - 1):\n", "            # Calculate average similarity with previous sentences\n", "            prev_similarities = similarity_matrix[i, max(0, i-3):i]\n", "            avg_prev_sim = np.mean(prev_similarities) if len(prev_similarities) > 0 else 0\n", "            \n", "            # Calculate average similarity with next sentences\n", "            next_similarities = similarity_matrix[i, i+1:min(len(sentences), i+4)]\n", "            avg_next_sim = np.mean(next_similarities) if len(next_similarities) > 0 else 0\n", "            \n", "            # If similarity drops significantly, it might be a topic boundary\n", "            if avg_prev_sim > self.similarity_threshold and avg_next_sim < self.similarity_threshold:\n", "                boundaries.append(i)\n", "        \n", "        boundaries.append(len(sentences))  # Always end with last sentence\n", "        \n", "        print(f\"🎯 Found {len(boundaries)-1} topic boundaries\")\n", "        return boundaries\n", "\n", "print(\"✅ Semantic analysis methods added!\")\n", "print(\"🎯 New capabilities:\")\n", "print(\"   • Intelligent sentence splitting\")\n", "print(\"   • Semantic similarity calculation\")\n", "print(\"   • Topic boundary detection\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 4}