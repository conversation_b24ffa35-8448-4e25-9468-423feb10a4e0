# 🛠️ Step 1.1: Import Required Libraries
# Each library serves a specific purpose in our PDF processing pipeline

import os
import sys
import fitz  # PyMuPDF - main PDF processing
import pdfplumber  # Table extraction specialist
import pytesseract  # OCR for images
from PIL import Image  # Image processing
import pandas as pd  # Data manipulation
import numpy as np
from pathlib import Path
import json
from typing import List, Dict, Any, Tuple
import warnings
warnings.filterwarnings('ignore')

print("✅ Core libraries imported!")
print("🎯 What each library does:")
print("   • fitz (PyMuPDF): Extract text, images, and metadata")
print("   • pdfplumber: Specialized table extraction")
print("   • pytesseract: OCR for reading text from images")
print("   • PIL: Image processing and manipulation")

# 📊 Visualize Vector Similarities
# Create a similarity matrix and visualize it

vector_names = list(vectors.keys())
n_vectors = len(vector_names)

# Create similarity matrix
similarity_matrix = np.zeros((n_vectors, n_vectors))

for i, name1 in enumerate(vector_names):
    for j, name2 in enumerate(vector_names):
        similarity_matrix[i][j] = calculate_cosine_similarity(vectors[name1], vectors[name2])

# Plot heatmap
plt.figure(figsize=(10, 8))
sns.heatmap(similarity_matrix, 
            xticklabels=vector_names, 
            yticklabels=vector_names,
            annot=True, 
            cmap='RdYlBu_r',
            center=0.5,
            square=True,
            fmt='.3f')
plt.title('🔥 Cosine Similarity Matrix\n(Darker = More Similar)', fontsize=14, fontweight='bold')
plt.xlabel('Concepts', fontweight='bold')
plt.ylabel('Concepts', fontweight='bold')
plt.tight_layout()
plt.show()

print("\n💡 Insights:")
print("• 'cat' and 'kitten' are most similar (both felines)")
print("• 'car' and 'vehicle' are highly similar (both transportation)")
print("• Animals vs vehicles show low similarity")

# 📊 Visualize Vector Similarities
# Create a similarity matrix and visualize it

vector_names = list(vectors.keys())
n_vectors = len(vector_names)

# Create similarity matrix
similarity_matrix = np.zeros((n_vectors, n_vectors))

for i, name1 in enumerate(vector_names):
    for j, name2 in enumerate(vector_names):
        similarity_matrix[i][j] = calculate_cosine_similarity(vectors[name1], vectors[name2])

# Plot heatmap
plt.figure(figsize=(10, 8))
sns.heatmap(similarity_matrix, 
            xticklabels=vector_names, 
            yticklabels=vector_names,
            annot=True, 
            cmap='RdYlBu_r',
            center=0.5,
            square=True,
            fmt='.3f')
plt.title('🔥 Cosine Similarity Matrix\n(Darker = More Similar)', fontsize=14, fontweight='bold')
plt.xlabel('Concepts', fontweight='bold')
plt.ylabel('Concepts', fontweight='bold')
plt.tight_layout()
plt.show()

print("\n💡 Insights:")
print("• 'cat' and 'kitten' are most similar (both felines)")
print("• 'car' and 'vehicle' are highly similar (both transportation)")
print("• Animals vs vehicles show low similarity")

# 🔧 Step 1.2: Setup Directories and Configuration
# Organize our workspace for efficient processing

# Create directory structure
base_dir = Path("../data")
pdf_dir = base_dir / "pdfs"
processed_dir = base_dir / "processed"
images_dir = processed_dir / "images"
tables_dir = processed_dir / "tables"
text_dir = processed_dir / "text"

# Create directories if they don't exist
for directory in [pdf_dir, processed_dir, images_dir, tables_dir, text_dir]:
    directory.mkdir(parents=True, exist_ok=True)
    print(f"📁 Created/verified: {directory}")

print("\n✅ Directory structure ready!")
print("🎯 Organization:")
print("   • pdfs/: Input PDF files")
print("   • processed/text/: Extracted text content")
print("   • processed/images/: Extracted images")
print("   • processed/tables/: Extracted tables")

# 📊 Step 1.3: Check Available PDF Files
# See what PDFs we have to work with

pdf_files = list(pdf_dir.glob("*.pdf"))

if pdf_files:
    print(f"📚 Found {len(pdf_files)} PDF files:")
    total_pages = 0
    
    for i, pdf_file in enumerate(pdf_files, 1):
        try:
            doc = fitz.open(pdf_file)
            page_count = len(doc)
            file_size = pdf_file.stat().st_size / (1024 * 1024)  # MB
            doc.close()
            
            print(f"   {i}. {pdf_file.name}")
            print(f"      📄 Pages: {page_count}")
            print(f"      💾 Size: {file_size:.1f} MB")
            
            total_pages += page_count
            
        except Exception as e:
            print(f"   ❌ Error reading {pdf_file.name}: {e}")
    
    print(f"\n📊 Total pages across all PDFs: {total_pages}")
    
    if total_pages >= 200:
        print("✅ Requirement met: 200+ pages available!")
    else:
        print(f"⚠️ Need more pages: {200 - total_pages} pages short")
        print("💡 Add more PDF files to meet the 200+ page requirement")
        
else:
    print("❌ No PDF files found!")
    print("💡 Please add PDF files to the data/pdfs/ directory")
    print("📋 Requirements:")
    print("   • At least 200 pages total")
    print("   • Mix of text, images, and tables")
    print("   • Various document types (reports, papers, etc.)")

# 📊 Visualize Vector Similarities
# Create a similarity matrix and visualize it

vector_names = list(vectors.keys())
n_vectors = len(vector_names)

# Create similarity matrix
similarity_matrix = np.zeros((n_vectors, n_vectors))

for i, name1 in enumerate(vector_names):
    for j, name2 in enumerate(vector_names):
        similarity_matrix[i][j] = calculate_cosine_similarity(vectors[name1], vectors[name2])

# Plot heatmap
plt.figure(figsize=(10, 8))
sns.heatmap(similarity_matrix, 
            xticklabels=vector_names, 
            yticklabels=vector_names,
            annot=True, 
            cmap='RdYlBu_r',
            center=0.5,
            square=True,
            fmt='.3f')
plt.title('🔥 Cosine Similarity Matrix\n(Darker = More Similar)', fontsize=14, fontweight='bold')
plt.xlabel('Concepts', fontweight='bold')
plt.ylabel('Concepts', fontweight='bold')
plt.tight_layout()
plt.show()

print("\n💡 Insights:")
print("• 'cat' and 'kitten' are most similar (both felines)")
print("• 'car' and 'vehicle' are highly similar (both transportation)")
print("• Animals vs vehicles show low similarity")

# 🔍 Step 2.1: PDF Content Analysis Function
# Analyze what types of content each PDF contains

def analyze_pdf_content(pdf_path: Path) -> Dict[str, Any]:
    """
    Analyze PDF content to understand what we're working with
    
    Returns:
        Dictionary with content analysis results
    """
    analysis = {
        'filename': pdf_path.name,
        'total_pages': 0,
        'text_pages': 0,
        'image_count': 0,
        'table_count': 0,
        'total_text_length': 0,
        'has_searchable_text': False,
        'content_types': []
    }
    
    try:
        # Open with PyMuPDF for general analysis
        doc = fitz.open(pdf_path)
        analysis['total_pages'] = len(doc)
        
        print(f"🔍 Analyzing {pdf_path.name}...")
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            
            # Check for text content
            text = page.get_text()
            if text.strip():
                analysis['text_pages'] += 1
                analysis['total_text_length'] += len(text)
                analysis['has_searchable_text'] = True
            
            # Check for images
            image_list = page.get_images()
            analysis['image_count'] += len(image_list)
        
        doc.close()
        
        # Use pdfplumber for table detection
        with pdfplumber.open(pdf_path) as pdf:
            for page in pdf.pages:
                tables = page.find_tables()
                analysis['table_count'] += len(tables)
        
        # Determine content types
        if analysis['text_pages'] > 0:
            analysis['content_types'].append('text')
        if analysis['image_count'] > 0:
            analysis['content_types'].append('images')
        if analysis['table_count'] > 0:
            analysis['content_types'].append('tables')
            
        return analysis
        
    except Exception as e:
        print(f"❌ Error analyzing {pdf_path.name}: {e}")
        return analysis

print("✅ PDF analysis function ready!")
print("🎯 This function will tell us:")
print("   • How many pages have text vs images")
print("   • Number of tables and images")
print("   • Whether text is searchable (not scanned)")
print("   • Content type distribution")

# 📊 Step 2.2: Analyze All PDF Files
# Get detailed content analysis for each PDF

if pdf_files:
    print("📊 Analyzing content of all PDF files...\n")
    
    all_analyses = []
    total_stats = {
        'total_pages': 0,
        'total_images': 0,
        'total_tables': 0,
        'total_text_length': 0,
        'searchable_pdfs': 0
    }
    
    for pdf_file in pdf_files:
        analysis = analyze_pdf_content(pdf_file)
        all_analyses.append(analysis)
        
        # Update totals
        total_stats['total_pages'] += analysis['total_pages']
        total_stats['total_images'] += analysis['image_count']
        total_stats['total_tables'] += analysis['table_count']
        total_stats['total_text_length'] += analysis['total_text_length']
        if analysis['has_searchable_text']:
            total_stats['searchable_pdfs'] += 1
        
        # Display results
        print(f"📄 {analysis['filename']}:")
        print(f"   📊 Pages: {analysis['total_pages']} ({analysis['text_pages']} with text)")
        print(f"   🖼️ Images: {analysis['image_count']}")
        print(f"   📋 Tables: {analysis['table_count']}")
        print(f"   📝 Text length: {analysis['total_text_length']:,} characters")
        print(f"   🔍 Searchable: {'✅' if analysis['has_searchable_text'] else '❌'}")
        print(f"   📂 Content types: {', '.join(analysis['content_types'])}")
        print()
    
    # Summary statistics
    print("📈 OVERALL STATISTICS:")
    print("=" * 40)
    print(f"📚 Total PDFs: {len(pdf_files)}")
    print(f"📄 Total pages: {total_stats['total_pages']}")
    print(f"🖼️ Total images: {total_stats['total_images']}")
    print(f"📋 Total tables: {total_stats['total_tables']}")
    print(f"📝 Total text: {total_stats['total_text_length']:,} characters")
    print(f"🔍 Searchable PDFs: {total_stats['searchable_pdfs']}/{len(pdf_files)}")
    
    # Check requirements
    print("\n✅ REQUIREMENT CHECK:")
    print(f"📄 Pages requirement: {total_stats['total_pages']} >= 200? {'✅' if total_stats['total_pages'] >= 200 else '❌'}")
    print(f"🖼️ Has images: {'✅' if total_stats['total_images'] > 0 else '❌'}")
    print(f"📋 Has tables: {'✅' if total_stats['total_tables'] > 0 else '❌'}")
    print(f"📝 Has text: {'✅' if total_stats['total_text_length'] > 0 else '❌'}")
    
else:
    print("⚠️ No PDF files to analyze. Please add PDFs to data/pdfs/ directory.")