{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🔍 Vector Index Comparison - Speed vs Accuracy Trade-offs\n", "\n", "## 🤔 What Are Vector Indexes?\n", "\n", "**The Problem**: With millions of vectors, searching becomes slow\n", "- **Brute force search**: Compare query with EVERY vector\n", "- **Time complexity**: O(n) - gets slower as data grows\n", "- **Real-world impact**: Seconds to minutes for large datasets\n", "\n", "**The Solution**: Smart indexing structures\n", "- **Pre-organize vectors**: Create efficient search structures\n", "- **Trade accuracy for speed**: Find \"good enough\" matches quickly\n", "- **Scalable**: Handle millions/billions of vectors\n", "\n", "## 🏆 The Three Index Champions\n", "\n", "### 🎯 **Flat Index (Brute Force)**\n", "- **How it works**: Compare query with every single vector\n", "- **Accuracy**: 100% - finds the absolute best matches\n", "- **Speed**: Slow - O(n) time complexity\n", "- **Memory**: Low - just stores vectors\n", "- **Best for**: Small datasets, when accuracy is critical\n", "\n", "### ⚡ **HNSW (Hierarchical Navigable Small World)**\n", "- **How it works**: Creates a multi-layer graph of connections\n", "- **Accuracy**: 95-99% - very close to perfect\n", "- **Speed**: Very fast - O(log n) time complexity\n", "- **Memory**: High - stores graph structure\n", "- **Best for**: Real-time applications, large datasets\n", "\n", "### 🗂️ **IVF (Inverted File)**\n", "- **How it works**: Groups similar vectors into clusters\n", "- **Accuracy**: 90-95% - good approximation\n", "- **Speed**: Fast - searches only relevant clusters\n", "- **Memory**: Medium - stores cluster information\n", "- **Best for**: Memory-constrained environments\n", "\n", "## 🎯 What We'll Discover\n", "We'll implement all three indexes and measure:\n", "- **Search speed**: How fast each index responds\n", "- **Accuracy**: How well they find the best matches\n", "- **Memory usage**: How much RAM they consume\n", "- **Build time**: How long setup takes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🛠️ Step 1.1: Import Index Comparison Libraries\n", "# We need tools for building, testing, and comparing different index types\n", "\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import time\n", "import psutil\n", "from pathlib import Path\n", "import json\n", "from typing import List, Dict, Any, Tuple\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Vector similarity and indexing\n", "from sklearn.metrics.pairwise import cosine_similarity\n", "import faiss  # For local index implementations\n", "\n", "# Embeddings\n", "from langchain_huggingface import HuggingFaceEmbeddings\n", "\n", "# Memory profiling\n", "from memory_profiler import profile\n", "\n", "print(\"✅ Index comparison libraries imported!\")\n", "print(\"🎯 What each tool does:\")\n", "print(\"   • FAISS: High-performance vector indexing library\")\n", "print(\"   • psutil: System resource monitoring\")\n", "print(\"   • memory_profiler: Detailed memory usage tracking\")\n", "print(\"   • matplot<PERSON>b/seaborn: Performance visualization\")\n", "\n", "# Set up plotting style\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "print(\"📊 Visualization setup complete!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🧪 Step 1.2: Create Test Dataset\n", "# Generate a realistic dataset to test our indexes\n", "\n", "def create_test_dataset(n_vectors: int = 10000, dimension: int = 384) -> Tuple[np.n<PERSON><PERSON>, List[str]]:\n", "    \"\"\"\n", "    Create a test dataset of vectors with metadata\n", "    \n", "    Args:\n", "        n_vectors: Number of vectors to generate\n", "        dimension: Dimension of each vector\n", "    \n", "    Returns:\n", "        Tuple of (vectors, metadata)\n", "    \"\"\"\n", "    print(f\"🧪 Creating test dataset: {n_vectors} vectors x {dimension} dimensions\")\n", "    \n", "    # Generate random vectors (normalized)\n", "    vectors = np.random.randn(n_vectors, dimension).astype(np.float32)\n", "    \n", "    # Normalize vectors (important for cosine similarity)\n", "    norms = np.linalg.norm(vectors, axis=1, keepdims=True)\n", "    vectors = vectors / norms\n", "    \n", "    # Create metadata\n", "    metadata = [f\"document_{i}\" for i in range(n_vectors)]\n", "    \n", "    print(f\"✅ Test dataset created!\")\n", "    print(f\"📊 Shape: {vectors.shape}\")\n", "    print(f\"📏 Vector norms (should be ~1.0): {np.mean(np.linalg.norm(vectors, axis=1)):.3f}\")\n", "    \n", "    return vectors, metadata\n", "\n", "# Create our test dataset\n", "print(\"🎯 Generating test vectors for index comparison...\")\n", "test_vectors, test_metadata = create_test_dataset(n_vectors=5000, dimension=384)\n", "\n", "# Create query vectors (subset of test vectors + some new ones)\n", "n_queries = 100\n", "query_vectors = np.vstack([\n", "    test_vectors[:50],  # 50 vectors that exist in the dataset\n", "    np.random.randn(50, 384).astype(np.float32)  # 50 new vectors\n", "])\n", "\n", "# Normalize query vectors\n", "query_norms = np.linalg.norm(query_vectors, axis=1, keepdims=True)\n", "query_vectors = query_vectors / query_norms\n", "\n", "print(f\"🔍 Created {len(query_vectors)} query vectors for testing\")\n", "print(f\"   • 50 vectors that exist in dataset (should get perfect matches)\")\n", "print(f\"   • 50 new vectors (will test approximate matching)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🏗️ Step 2: Building the Three Index Types\n", "\n", "### 🎯 Implementation Strategy:\n", "We'll use **FAISS** (Facebook AI Similarity Search) to implement all three index types:\n", "\n", "1. **IndexFlatIP**: Flat index with inner product (our baseline)\n", "2. **IndexHNSWFlat**: HNSW index for fast approximate search\n", "3. **IndexIVFFlat**: IVF index for memory-efficient search\n", "\n", "### 🔧 Why FAISS?\n", "- **Industry standard**: Used by Facebook, Google, Microsoft\n", "- **Highly optimized**: C++ implementation with Python bindings\n", "- **Multiple algorithms**: Supports all major indexing methods\n", "- **Production ready**: Handles billions of vectors\n", "\n", "### 📊 What We'll Measure:\n", "- **Build time**: How long to create the index\n", "- **Memory usage**: RAM consumption\n", "- **Search time**: Query response time\n", "- **Accuracy**: How well it finds true nearest neighbors"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🏗️ Step 2.1: Index Builder Class\n", "# Create a unified interface for building and testing different index types\n", "\n", "class IndexComparator:\n", "    \"\"\"\n", "    Compare different vector index types for performance and accuracy\n", "    \"\"\"\n", "    \n", "    def __init__(self, vectors: np.ndarray, metadata: List[str]):\n", "        self.vectors = vectors\n", "        self.metadata = metadata\n", "        self.dimension = vectors.shape[1]\n", "        self.n_vectors = vectors.shape[0]\n", "        \n", "        # Storage for different indexes\n", "        self.indexes = {}\n", "        self.build_times = {}\n", "        self.memory_usage = {}\n", "        \n", "        print(f\"🏗️ Index Comparator initialized\")\n", "        print(f\"📊 Dataset: {self.n_vectors} vectors x {self.dimension} dimensions\")\n", "        print(f\"💾 Memory footprint: {self.vectors.nbytes / 1024 / 1024:.1f} MB\")\n", "    \n", "    def build_flat_index(self) -> faiss.Index:\n", "        \"\"\"\n", "        Build a flat (brute force) index - our accuracy baseline\n", "        \"\"\"\n", "        print(\"\\n🎯 Building Flat Index (Brute Force)...\")\n", "        print(\"   • Searches every vector for exact results\")\n", "        print(\"   • 100% accuracy but slowest speed\")\n", "        print(\"   • Best for: Small datasets, when accuracy is critical\")\n", "        \n", "        start_time = time.time()\n", "        start_memory = psutil.Process().memory_info().rss / 1024 / 1024\n", "        \n", "        # Create flat index (exact search)\n", "        index = faiss.IndexFlatIP(self.dimension)  # Inner Product for cosine similarity\n", "        index.add(self.vectors)\n", "        \n", "        build_time = time.time() - start_time\n", "        end_memory = psutil.Process().memory_info().rss / 1024 / 1024\n", "        memory_used = end_memory - start_memory\n", "        \n", "        self.indexes['Flat'] = index\n", "        self.build_times['Flat'] = build_time\n", "        self.memory_usage['Flat'] = memory_used\n", "        \n", "        print(f\"✅ Flat index built in {build_time:.3f} seconds\")\n", "        print(f\"💾 Memory used: {memory_used:.1f} MB\")\n", "        print(f\"📊 Index size: {index.ntotal} vectors\")\n", "        \n", "        return index\n", "    \n", "    def build_hnsw_index(self, M: int = 16, ef_construction: int = 200) -> faiss.Index:\n", "        \"\"\"\n", "        Build HNSW (Hierarchical Navigable Small World) index\n", "        \n", "        Args:\n", "            M: Number of connections per node (higher = better accuracy, more memory)\n", "            ef_construction: Search width during construction (higher = better quality)\n", "        \"\"\"\n", "        print(\"\\n⚡ Building HNSW Index (Fast Approximate)...\")\n", "        print(\"   • Creates a multi-layer graph structure\")\n", "        print(\"   • 95-99% accuracy with much faster speed\")\n", "        print(\"   • Best for: Real-time applications, large datasets\")\n", "        print(f\"   • Parameters: M={M}, ef_construction={ef_construction}\")\n", "        \n", "        start_time = time.time()\n", "        start_memory = psutil.Process().memory_info().rss / 1024 / 1024\n", "        \n", "        # Create HNSW index\n", "        index = faiss.IndexHNSWFlat(self.dimension, M)\n", "        index.hnsw.efConstruction = ef_construction\n", "        index.add(self.vectors)\n", "        \n", "        build_time = time.time() - start_time\n", "        end_memory = psutil.Process().memory_info().rss / 1024 / 1024\n", "        memory_used = end_memory - start_memory\n", "        \n", "        self.indexes['HNSW'] = index\n", "        self.build_times['HNSW'] = build_time\n", "        self.memory_usage['HNSW'] = memory_used\n", "        \n", "        print(f\"✅ HNSW index built in {build_time:.3f} seconds\")\n", "        print(f\"💾 Memory used: {memory_used:.1f} MB\")\n", "        print(f\"📊 Index size: {index.ntotal} vectors\")\n", "        print(f\"🔗 Graph connections: {index.hnsw.max_level + 1} levels\")\n", "        \n", "        return index\n", "    \n", "    def build_ivf_index(self, nlist: int = 100) -> faiss.Index:\n", "        \"\"\"\n", "        Build IVF (Inverted File) index\n", "        \n", "        Args:\n", "            nlist: Number of clusters (higher = better accuracy, slower search)\n", "        \"\"\"\n", "        print(\"\\n🗂️ Building IVF Index (Memory Efficient)...\")\n", "        print(\"   • Groups similar vectors into clusters\")\n", "        print(\"   • 90-95% accuracy with good speed\")\n", "        print(\"   • Best for: Memory-constrained environments\")\n", "        print(f\"   • Parameters: nlist={nlist} clusters\")\n", "        \n", "        start_time = time.time()\n", "        start_memory = psutil.Process().memory_info().rss / 1024 / 1024\n", "        \n", "        # Create IVF index\n", "        quantizer = faiss.IndexFlatIP(self.dimension)\n", "        index = faiss.IndexIVFFlat(quantizer, self.dimension, nlist)\n", "        \n", "        # Train the index (IVF needs training to learn clusters)\n", "        print(\"   🎓 Training IVF clusters...\")\n", "        index.train(self.vectors)\n", "        index.add(self.vectors)\n", "        \n", "        build_time = time.time() - start_time\n", "        end_memory = psutil.Process().memory_info().rss / 1024 / 1024\n", "        memory_used = end_memory - start_memory\n", "        \n", "        self.indexes['IVF'] = index\n", "        self.build_times['IVF'] = build_time\n", "        self.memory_usage['IVF'] = memory_used\n", "        \n", "        print(f\"✅ IVF index built in {build_time:.3f} seconds\")\n", "        print(f\"💾 Memory used: {memory_used:.1f} MB\")\n", "        print(f\"📊 Index size: {index.ntotal} vectors\")\n", "        print(f\"🗂️ Clusters: {index.nlist}\")\n", "        \n", "        return index\n", "\n", "print(\"✅ Index Comparator class created!\")\n", "print(\"🎯 Ready to build and compare all three index types\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🚀 Step 2.2: Build All Index Types\n", "# Create instances of all three index types for comparison\n", "\n", "print(\"🏗️ Building all index types for comparison...\")\n", "print(\"=\" * 60)\n", "\n", "# Initialize the comparator\n", "comparator = IndexComparator(test_vectors, test_metadata)\n", "\n", "# Build all three index types\n", "flat_index = comparator.build_flat_index()\n", "hnsw_index = comparator.build_hnsw_index(M=16, ef_construction=200)\n", "ivf_index = comparator.build_ivf_index(nlist=100)\n", "\n", "print(\"\\n\" + \"=\" * 60)\n", "print(\"📊 BUILD TIME COMPARISON:\")\n", "print(\"-\" * 30)\n", "for index_name, build_time in comparator.build_times.items():\n", "    print(f\"{index_name:>8}: {build_time:>8.3f} seconds\")\n", "\n", "print(\"\\n💾 MEMORY USAGE COMPARISON:\")\n", "print(\"-\" * 30)\n", "for index_name, memory in comparator.memory_usage.items():\n", "    print(f\"{index_name:>8}: {memory:>8.1f} MB\")\n", "\n", "print(\"\\n✅ All indexes built successfully!\")\n", "print(\"🎯 Next: We'll test search performance and accuracy\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 4}