# ===============================================================================
# 🚀 ADVANCED PDF RAG SYSTEM - DEPENDENCIES
# ===============================================================================
# Complete requirements for Assignment 2: Advanced PDF processing with multiple
# vector databases, indexing methods, and DOCX generation
# ===============================================================================

# Core Python packages
numpy==1.24.3
pandas==2.0.3
matplotlib==3.7.2
seaborn==0.12.2
scikit-learn==1.3.0
scipy==1.11.1
tqdm==4.66.1
python-dotenv==1.0.0
pyyaml==6.0.1

# LangChain ecosystem
langchain==0.1.20
langchain-community==0.0.38
langchain-core==0.1.52
langchain-text-splitters==0.0.1
langchain-huggingface==0.0.3
langchain-google-genai==1.0.6
langchain-openai==0.1.8

# PDF Processing
pypdf==3.15.2
pymupdf==1.23.5              # Advanced PDF processing with images/tables
pdfplumber==0.9.0            # Table extraction
pdf2image==1.16.3            # PDF to image conversion
pytesseract==0.3.10          # OCR for images
pillow==10.0.0               # Image processing

# Document Processing
python-docx==0.8.11          # DOCX generation
docx2txt==0.8                # DOCX text extraction
unstructured==0.10.25        # Advanced document parsing
unstructured[pdf]==0.10.25   # PDF-specific parsing

# Embedding Models
sentence-transformers>=2.6.0
transformers>=4.33.2
torch>=2.0.1
huggingface-hub>=0.23.0

# Vector Databases
faiss-cpu==1.7.4             # Local vector search

# MongoDB Atlas Vector Search
pymongo==4.5.0
motor==3.3.1                 # Async MongoDB driver

# AstraDB (Cassandra)
cassandra-driver==3.28.0
astrapy==0.7.0               # AstraDB Python client

# OpenSearch
opensearch-py==2.3.1         # OpenSearch client
requests-aws4auth==1.2.3     # AWS authentication

# Milvus
pymilvus==2.3.1              # Milvus vector database

# Re-ranking
rank-bm25==0.2.2             # BM25 implementation

# API Clients
openai==1.35.5               # OpenAI API
google-generativeai==0.5.4   # Google AI API

# Performance and Monitoring
psutil==5.9.5                # System monitoring
memory-profiler==0.61.0      # Memory usage tracking
line-profiler==4.1.1         # Line-by-line profiling

# Utilities
requests==2.31.0
beautifulsoup4==4.12.2
lxml==4.9.3                  # XML/HTML parsing
openpyxl==3.1.2              # Excel file handling

# Jupyter and Development
jupyter==1.0.0
ipykernel==6.25.0
ipywidgets==8.1.0
notebook==7.0.2

# Testing
pytest==7.4.2
pytest-asyncio==0.21.1

# ===============================================================================
# 📋 INSTALLATION NOTES
# ===============================================================================
#
# 🔧 SYSTEM DEPENDENCIES (Install before pip):
#
# For PDF processing with images:
#   • Ubuntu/Debian: sudo apt-get install tesseract-ocr poppler-utils
#   • macOS: brew install tesseract poppler
#   • Windows: Download from respective websites
#
# For advanced document parsing:
#   • Ubuntu/Debian: sudo apt-get install libmagic1
#   • macOS: brew install libmagic
#   • Windows: pip install python-magic-bin
#
# ===============================================================================
# 🗄️ VECTOR DATABASE SPECIFIC REQUIREMENTS
# ===============================================================================
#
# Choose ONE vector database and install additional requirements:
#
# 📊 MongoDB Atlas:
#   • No additional requirements (cloud-based)
#   • Sign up at: https://cloud.mongodb.com
#
# 🌟 AstraDB:
#   • No additional requirements (cloud-based)
#   • Sign up at: https://astra.datastax.com
#
# 🔍 OpenSearch:
#   • For local: docker run -p 9200:9200 opensearchproject/opensearch
#   • For cloud: AWS OpenSearch Service
#
# 🚀 Milvus:
#   • For local: docker run -p 19530:19530 milvusdb/milvus
#   • For cloud: Zilliz Cloud
#
# ===============================================================================
# 💰 COST CONSIDERATIONS
# ===============================================================================
#
# 🆓 FREE OPTIONS:
#   • HuggingFace models: Completely free
#   • MongoDB Atlas: 512MB free tier
#   • AstraDB: $25 free credits
#   • Local vector databases: Free but need resources
#
# 💵 PAID OPTIONS:
#   • OpenAI API: ~$0.0001 per 1K tokens
#   • Google AI: Free tier, then paid
#   • Cloud vector databases: Pay per usage
#
# ===============================================================================
# 🚀 QUICK INSTALL
# ===============================================================================
#
# conda create -n advanced-rag python=3.9
# conda activate advanced-rag
# pip install -r requirements.txt
#
# ===============================================================================
