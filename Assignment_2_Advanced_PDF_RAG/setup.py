#!/usr/bin/env python3
"""
🚀 Advanced PDF RAG System - Setup Script
=========================================

This script sets up the complete environment for Assignment 2:
- Creates directory structure
- Installs dependencies
- Validates environment
- Downloads sample PDFs (optional)
- Tests all components

Usage:
    python setup.py
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path
import requests
import zipfile
from typing import List, Dict, Any

class AdvancedRAGSetup:
    """Setup manager for the Advanced PDF RAG system"""
    
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.errors = []
        self.warnings = []
        
    def print_header(self, title: str):
        """Print a formatted header"""
        print("\n" + "=" * 60)
        print(f"🚀 {title}")
        print("=" * 60)
    
    def print_step(self, step: str):
        """Print a step indicator"""
        print(f"\n📋 {step}")
        print("-" * 40)
    
    def check_python_version(self) -> bool:
        """Check if Python version is compatible"""
        self.print_step("Checking Python Version")
        
        version = sys.version_info
        if version.major == 3 and version.minor >= 8:
            print(f"✅ Python {version.major}.{version.minor}.{version.micro} - Compatible")
            return True
        else:
            print(f"❌ Python {version.major}.{version.minor}.{version.micro} - Requires Python 3.8+")
            self.errors.append("Python version too old")
            return False
    
    def create_directory_structure(self) -> bool:
        """Create the project directory structure"""
        self.print_step("Creating Directory Structure")
        
        directories = [
            "data/pdfs",
            "data/processed/text",
            "data/processed/images", 
            "data/processed/tables",
            "data/outputs",
            "src",
            "configs",
            "tests",
            "logs"
        ]
        
        try:
            for directory in directories:
                dir_path = self.base_dir / directory
                dir_path.mkdir(parents=True, exist_ok=True)
                print(f"📁 Created: {directory}")
            
            print("✅ Directory structure created successfully")
            return True
            
        except Exception as e:
            print(f"❌ Error creating directories: {e}")
            self.errors.append(f"Directory creation failed: {e}")
            return False
    
    def install_dependencies(self) -> bool:
        """Install required Python packages"""
        self.print_step("Installing Dependencies")
        
        try:
            # Check if requirements.txt exists
            requirements_file = self.base_dir / "requirements.txt"
            if not requirements_file.exists():
                print("❌ requirements.txt not found")
                self.errors.append("requirements.txt missing")
                return False
            
            print("📦 Installing packages from requirements.txt...")
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ Dependencies installed successfully")
                return True
            else:
                print(f"❌ Installation failed: {result.stderr}")
                self.errors.append(f"Pip install failed: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Error installing dependencies: {e}")
            self.errors.append(f"Dependency installation failed: {e}")
            return False
    
    def setup_environment_file(self) -> bool:
        """Setup .env file from template"""
        self.print_step("Setting Up Environment File")
        
        env_template = self.base_dir / ".env.template"
        env_file = self.base_dir / ".env"
        
        try:
            if env_template.exists():
                if not env_file.exists():
                    shutil.copy(env_template, env_file)
                    print("✅ Created .env file from template")
                    print("⚠️ Please edit .env file with your API keys")
                    self.warnings.append("Edit .env file with your API keys")
                else:
                    print("✅ .env file already exists")
                return True
            else:
                print("❌ .env.template not found")
                self.errors.append(".env.template missing")
                return False
                
        except Exception as e:
            print(f"❌ Error setting up .env file: {e}")
            self.errors.append(f"Environment setup failed: {e}")
            return False
    
    def test_imports(self) -> bool:
        """Test if all required packages can be imported"""
        self.print_step("Testing Package Imports")
        
        required_packages = [
            ("numpy", "np"),
            ("pandas", "pd"),
            ("langchain_huggingface", "HuggingFaceEmbeddings"),
            ("faiss", None),
            ("pymupdf", "fitz"),
            ("pdfplumber", None),
            ("pytesseract", None),
            ("PIL", "Image"),
            ("python_docx", "docx")
        ]
        
        failed_imports = []
        
        for package, alias in required_packages:
            try:
                if alias:
                    exec(f"from {package} import {alias}")
                else:
                    exec(f"import {package}")
                print(f"✅ {package}")
            except ImportError as e:
                print(f"❌ {package}: {e}")
                failed_imports.append(package)
        
        if failed_imports:
            print(f"\n❌ Failed to import: {', '.join(failed_imports)}")
            self.errors.append(f"Import failures: {failed_imports}")
            return False
        else:
            print("\n✅ All packages imported successfully")
            return True
    
    def download_sample_pdfs(self) -> bool:
        """Download sample PDF files for testing"""
        self.print_step("Downloading Sample PDFs (Optional)")
        
        # Sample PDFs with different content types
        sample_pdfs = [
            {
                "name": "attention_is_all_you_need.pdf",
                "url": "https://arxiv.org/pdf/1706.03762.pdf",
                "description": "Transformer paper (text + diagrams)"
            },
            {
                "name": "bert_paper.pdf", 
                "url": "https://arxiv.org/pdf/1810.04805.pdf",
                "description": "BERT paper (text + tables)"
            }
        ]
        
        pdf_dir = self.base_dir / "data" / "pdfs"
        downloaded = 0
        
        for pdf_info in sample_pdfs:
            try:
                pdf_path = pdf_dir / pdf_info["name"]
                if pdf_path.exists():
                    print(f"✅ {pdf_info['name']} already exists")
                    downloaded += 1
                    continue
                
                print(f"📥 Downloading {pdf_info['name']}...")
                response = requests.get(pdf_info["url"], timeout=30)
                response.raise_for_status()
                
                with open(pdf_path, "wb") as f:
                    f.write(response.content)
                
                print(f"✅ Downloaded {pdf_info['name']} ({len(response.content) // 1024} KB)")
                downloaded += 1
                
            except Exception as e:
                print(f"⚠️ Failed to download {pdf_info['name']}: {e}")
                self.warnings.append(f"PDF download failed: {pdf_info['name']}")
        
        if downloaded > 0:
            print(f"\n✅ Downloaded {downloaded} sample PDFs")
            return True
        else:
            print("\n⚠️ No PDFs downloaded - you'll need to add your own")
            self.warnings.append("No sample PDFs available")
            return False
    
    def create_config_files(self) -> bool:
        """Create default configuration files"""
        self.print_step("Creating Configuration Files")
        
        configs = {
            "database_config.yaml": """
# Vector Database Configuration
vector_db:
  type: "milvus"  # Options: milvus, astradb, mongodb, opensearch
  
milvus:
  host: "localhost"
  port: 19530
  collection_name: "pdf_vectors"

astradb:
  api_endpoint: "${ASTRA_DB_API_ENDPOINT}"
  application_token: "${ASTRA_DB_APPLICATION_TOKEN}"
  keyspace: "pdf_rag"

mongodb:
  uri: "${MONGODB_URI}"
  database: "pdf_rag_db"
  collection: "pdf_vectors"

opensearch:
  host: "localhost"
  port: 9200
  index_name: "pdf_vectors"
""",
            "chunking_config.yaml": """
# Semantic Chunking Configuration
chunking:
  strategy: "semantic"  # Options: semantic, fixed, paragraph
  
semantic:
  max_chunk_size: 1000
  min_chunk_size: 100
  overlap_size: 50
  similarity_threshold: 0.7

fixed:
  chunk_size: 500
  overlap: 50

paragraph:
  max_paragraphs: 3
  min_words: 50
""",
            "model_config.yaml": """
# Model Configuration
embeddings:
  model_name: "all-MiniLM-L6-v2"
  device: "cpu"
  normalize: true

llm:
  provider: "google"  # Options: openai, google, local
  model: "gemini-pro"
  temperature: 0.0
  max_tokens: 2000

reranking:
  method: "bm25"  # Options: bm25, mmr
  top_k: 3
"""
        }
        
        config_dir = self.base_dir / "configs"
        
        try:
            for filename, content in configs.items():
                config_path = config_dir / filename
                if not config_path.exists():
                    with open(config_path, "w") as f:
                        f.write(content.strip())
                    print(f"✅ Created {filename}")
                else:
                    print(f"✅ {filename} already exists")
            
            print("✅ Configuration files ready")
            return True
            
        except Exception as e:
            print(f"❌ Error creating config files: {e}")
            self.errors.append(f"Config creation failed: {e}")
            return False
    
    def run_setup(self) -> bool:
        """Run the complete setup process"""
        self.print_header("Advanced PDF RAG System Setup")
        
        steps = [
            ("Python Version", self.check_python_version),
            ("Directory Structure", self.create_directory_structure),
            ("Dependencies", self.install_dependencies),
            ("Environment File", self.setup_environment_file),
            ("Package Imports", self.test_imports),
            ("Configuration Files", self.create_config_files),
            ("Sample PDFs", self.download_sample_pdfs)
        ]
        
        success_count = 0
        for step_name, step_func in steps:
            try:
                if step_func():
                    success_count += 1
            except Exception as e:
                print(f"❌ Unexpected error in {step_name}: {e}")
                self.errors.append(f"{step_name} failed: {e}")
        
        # Print summary
        self.print_header("Setup Summary")
        print(f"✅ Successful steps: {success_count}/{len(steps)}")
        
        if self.warnings:
            print(f"\n⚠️ Warnings ({len(self.warnings)}):")
            for warning in self.warnings:
                print(f"   • {warning}")
        
        if self.errors:
            print(f"\n❌ Errors ({len(self.errors)}):")
            for error in self.errors:
                print(f"   • {error}")
            print("\n💡 Please fix these errors before proceeding")
            return False
        else:
            print("\n🎉 Setup completed successfully!")
            print("\n🚀 Next steps:")
            print("   1. Edit .env file with your API keys")
            print("   2. Add PDF files to data/pdfs/ directory")
            print("   3. Start with notebooks/01_PDF_Processing.ipynb")
            return True

if __name__ == "__main__":
    setup = AdvancedRAGSetup()
    success = setup.run_setup()
    sys.exit(0 if success else 1)
