# ===============================================================================
# 🚀 ADVANCED PDF RAG SYSTEM - ENVIRONMENT VARIABLES
# ===============================================================================
# Configuration for Assignment 2: Advanced PDF processing with multiple
# vector databases, indexing methods, and DOCX generation
#
# INSTRUCTIONS:
# 1. Copy this file to .env (remove .template from filename)
# 2. Fill in the values for your chosen vector database
# 3. Add API keys for LLM services
# 4. Never commit the .env file to version control
# ===============================================================================

# ===============================================================================
# 🤖 AI MODEL API KEYS
# ===============================================================================

# 🤗 HuggingFace Token (FREE - for embedding models)
# Get from: https://huggingface.co/settings/tokens
# Purpose: Download and use embedding models locally
# Required: Yes (for best performance)
HF_TOKEN=your_huggingface_token_here

# 🌟 OpenAI API Key (PAID - for LLM generation)
# Get from: https://platform.openai.com/api-keys
# Purpose: High-quality text generation for RAG responses
# Cost: ~$0.002 per 1K tokens for GPT-3.5-turbo
# Required: Optional (can use Google AI instead)
OPENAI_API_KEY=your_openai_api_key_here

# 🎯 Google AI API Key (FREE tier available)
# Get from: https://makersuite.google.com/app/apikey
# Purpose: Free alternative to OpenAI for text generation
# Cost: FREE tier with generous limits
# Required: Recommended if no OpenAI key
GOOGLE_API_KEY=your_google_api_key_here

# ===============================================================================
# 🗄️ VECTOR DATABASE CONFIGURATIONS
# ===============================================================================
# Choose ONE vector database and configure its settings below

# 🚀 MILVUS (Recommended for learning)
# Purpose: Purpose-built vector database with excellent indexing
# Setup: docker run -p 19530:19530 milvusdb/milvus:latest
# Pros: Best performance, supports all index types
# Cons: Requires Docker setup
MILVUS_HOST=localhost
MILVUS_PORT=19530
MILVUS_COLLECTION_NAME=pdf_vectors

# ⭐ ASTRADB (DataStax Cassandra)
# Purpose: High-performance cloud vector database
# Setup: Sign up at https://astra.datastax.com
# Pros: $25 free credits, production-ready
# Cons: Learning curve for Cassandra concepts
ASTRA_DB_API_ENDPOINT=your_astra_db_endpoint_here
ASTRA_DB_APPLICATION_TOKEN=your_astra_db_token_here
ASTRA_DB_KEYSPACE=pdf_rag

# 🍃 MONGODB ATLAS VECTOR SEARCH
# Purpose: MongoDB with vector search capabilities
# Setup: Sign up at https://cloud.mongodb.com
# Pros: Familiar MongoDB interface, rich metadata queries
# Cons: Newer vector features, limited free tier
MONGODB_URI=your_mongodb_connection_string_here
MONGODB_DB_NAME=pdf_rag_db
MONGODB_COLLECTION_NAME=pdf_vectors

# 🔍 OPENSEARCH
# Purpose: Elasticsearch fork with vector search
# Setup: docker run -p 9200:9200 opensearchproject/opensearch:latest
# Pros: Excellent hybrid text + vector search
# Cons: Complex setup, resource intensive
OPENSEARCH_HOST=localhost
OPENSEARCH_PORT=9200
OPENSEARCH_USERNAME=admin
OPENSEARCH_PASSWORD=admin
OPENSEARCH_INDEX_NAME=pdf_vectors

# ===============================================================================
# 📊 PERFORMANCE AND MONITORING
# ===============================================================================

# Performance tracking
ENABLE_PERFORMANCE_LOGGING=true
LOG_LEVEL=INFO

# Batch processing settings
BATCH_SIZE=100
MAX_CONCURRENT_REQUESTS=5

# ===============================================================================
# 🔧 PROCESSING CONFIGURATIONS
# ===============================================================================

# PDF Processing
MAX_PDF_SIZE_MB=100
EXTRACT_IMAGES=true
EXTRACT_TABLES=true
OCR_ENABLED=true

# Chunking Configuration
CHUNK_SIZE=1000
CHUNK_OVERLAP=100
MIN_CHUNK_SIZE=50
SEMANTIC_SIMILARITY_THRESHOLD=0.7

# Retrieval Configuration
DEFAULT_TOP_K=5
RERANK_TOP_K=3
SIMILARITY_THRESHOLD=0.6

# ===============================================================================
# 📄 OUTPUT CONFIGURATION
# ===============================================================================

# DOCX Generation
OUTPUT_DIR=./outputs
INCLUDE_CITATIONS=true
INCLUDE_METADATA=true
MAX_OUTPUT_LENGTH=2000

# ===============================================================================
# 🔍 INDEX CONFIGURATION
# ===============================================================================

# Index types to create and compare
# Set to true for the indexes you want to test
CREATE_FLAT_INDEX=true
CREATE_HNSW_INDEX=true
CREATE_IVF_INDEX=true

# HNSW Parameters
HNSW_M=16
HNSW_EF_CONSTRUCTION=200
HNSW_EF_SEARCH=100

# IVF Parameters
IVF_NLIST=100
IVF_NPROBE=10

# ===============================================================================
# 🛡️ SECURITY AND RATE LIMITING
# ===============================================================================

# API Rate Limiting
OPENAI_RATE_LIMIT_RPM=60
GOOGLE_AI_RATE_LIMIT_RPM=60

# Security
ENABLE_REQUEST_LOGGING=false
MASK_SENSITIVE_DATA=true

# ===============================================================================
# 📋 CONFIGURATION GUIDE
# ===============================================================================
#
# 🎯 QUICK START CONFIGURATIONS:
#
# 🆓 FREE SETUP (No costs):
#   1. Use HF_TOKEN for embeddings
#   2. Use GOOGLE_API_KEY for LLM (free tier)
#   3. Use local Milvus: docker run -p 19530:19530 milvusdb/milvus
#
# 💰 PREMIUM SETUP (Best quality):
#   1. Use HF_TOKEN for embeddings
#   2. Use OPENAI_API_KEY for LLM (paid)
#   3. Use AstraDB for vector storage (free credits)
#
# 🏢 PRODUCTION SETUP:
#   1. Use dedicated embedding service
#   2. Use production LLM API
#   3. Use managed vector database
#
# ===============================================================================
# 🔧 TROUBLESHOOTING
# ===============================================================================
#
# ❌ Common Issues:
#
# 1. "Connection refused" errors:
#    • Check if Docker containers are running
#    • Verify host and port settings
#    • Check firewall settings
#
# 2. "Authentication failed" errors:
#    • Verify API keys are correct
#    • Check API key permissions
#    • Ensure no extra spaces in keys
#
# 3. "Rate limit exceeded" errors:
#    • Reduce batch sizes
#    • Add delays between requests
#    • Check API usage limits
#
# 4. "Out of memory" errors:
#    • Reduce chunk sizes
#    • Process fewer documents at once
#    • Use smaller embedding models
#
# ===============================================================================
# 💡 OPTIMIZATION TIPS
# ===============================================================================
#
# 🚀 Performance:
#   • Use local embeddings (HuggingFace) for speed
#   • Batch process documents for efficiency
#   • Choose appropriate index types for your use case
#
# 💰 Cost:
#   • Use Google AI free tier instead of OpenAI
#   • Process documents in batches to reduce API calls
#   • Use local vector databases for development
#
# 🎯 Quality:
#   • Use semantic chunking for better retrieval
#   • Implement re-ranking for improved relevance
#   • Fine-tune similarity thresholds for your data
#
# ===============================================================================
