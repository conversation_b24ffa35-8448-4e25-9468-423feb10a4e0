# 🚀 Assignment 2: Advanced PDF RAG System

## 📋 Assignment Overview

Build a comprehensive RAG system that processes multiple PDFs with text, images, and tables, implements advanced vector database techniques, and generates outputs in DOCX format.

## 🎯 Assignment Requirements

### 📚 **1. PDF Processing (200+ pages)**
- [ ] Process multiple PDF files with mixed content
- [ ] Extract text, images, and tables
- [ ] Handle complex document structures
- [ ] Maintain document metadata and structure

### 🔪 **2. Advanced Chunking**
- [ ] Implement semantic chunking techniques
- [ ] Preserve context across chunks
- [ ] Handle different content types appropriately
- [ ] Optimize chunk sizes for retrieval

### 🗄️ **3. Vector Database Implementation**
Choose ONE of these vector databases:
- [ ] **MongoDB Atlas Vector Search**
- [ ] **AstraDB (Cassandra)**
- [ ] **OpenSearch**
- [ ] **Milvus**

### 🔍 **4. Index Mechanisms (All Three)**
Implement and compare all three indexing approaches:
- [ ] **Flat Index**: Exact search, baseline performance
- [ ] **HNSW**: Hierarchical Navigable Small World
- [ ] **IVF**: Inverted File Index

### ⚡ **5. Retrieval Pipeline**
- [ ] Build efficient retrieval system
- [ ] Measure and compare retrieval times
- [ ] Calculate accuracy scores for similarity search
- [ ] Optimize for speed and relevance

### 🔄 **6. Re-ranking Implementation**
Choose ONE re-ranking method:
- [ ] **BM25**: Best Matching 25 algorithm
- [ ] **MMR**: Maximal Marginal Relevance

### 🤖 **7. LLM Integration**
- [ ] Create effective prompt templates
- [ ] Generate contextual responses
- [ ] Ensure grounded answers

### 📄 **8. DOCX Output Generation**
- [ ] Render final outputs in DOCX format
- [ ] Include source citations
- [ ] Maintain formatting and structure

## 📁 Project Structure

```
Assignment_2_Advanced_PDF_RAG/
├── README.md                           # This file
├── requirements.txt                    # Dependencies
├── .env.template                       # API keys template
├── notebooks/
│   ├── 01_PDF_Processing.ipynb         # PDF extraction and processing
│   ├── 02_Semantic_Chunking.ipynb     # Advanced chunking techniques
│   ├── 03_Vector_Database_Setup.ipynb # Database implementation
│   ├── 04_Index_Comparison.ipynb      # Flat, HNSW, IVF comparison
│   ├── 05_Retrieval_Pipeline.ipynb    # Retrieval and evaluation
│   ├── 06_Reranking_Methods.ipynb     # BM25 and MMR implementation
│   ├── 07_LLM_Integration.ipynb       # Prompt engineering and generation
│   └── 08_DOCX_Generation.ipynb       # Output formatting
├── src/
│   ├── __init__.py
│   ├── pdf_processor.py               # PDF processing utilities
│   ├── chunking.py                     # Semantic chunking implementation
│   ├── vector_db.py                   # Vector database interface
│   ├── indexing.py                     # Index implementations
│   ├── retrieval.py                   # Retrieval pipeline
│   ├── reranking.py                   # Re-ranking algorithms
│   └── docx_generator.py              # DOCX output generation
├── data/
│   ├── pdfs/                          # Input PDF files (200+ pages)
│   ├── processed/                     # Processed chunks and metadata
│   └── outputs/                       # Generated DOCX files
├── configs/
│   ├── database_config.yaml           # Vector DB configurations
│   ├── chunking_config.yaml           # Chunking parameters
│   └── model_config.yaml              # Model configurations
└── tests/
    ├── test_pdf_processing.py
    ├── test_chunking.py
    ├── test_retrieval.py
    └── test_performance.py
```

## 🚀 Getting Started

### 1. Environment Setup
```bash
# Create conda environment
conda create -n advanced-rag python=3.9
conda activate advanced-rag

# Install dependencies
pip install -r requirements.txt
```

### 2. API Keys Setup
```bash
# Copy template and add your keys
cp .env.template .env
# Edit .env with your API keys
```

### 3. Download Sample PDFs
- Find PDFs with 200+ pages total
- Include documents with text, images, and tables
- Place in `data/pdfs/` directory

### 4. Run Notebooks in Order
Start with `01_PDF_Processing.ipynb` and work through each notebook sequentially.

## 📊 Evaluation Metrics

### Performance Metrics
- **Retrieval Time**: Measure query response time
- **Accuracy Score**: Similarity search precision
- **Index Comparison**: Speed vs accuracy trade-offs
- **Re-ranking Impact**: Before/after relevance scores

### Quality Metrics
- **Answer Relevance**: How well answers match questions
- **Source Attribution**: Accuracy of citations
- **Context Preservation**: Maintaining document structure
- **Output Quality**: DOCX formatting and readability

## 🎯 Learning Objectives

By completing this assignment, you will:
1. **Master PDF Processing**: Handle complex document structures
2. **Understand Semantic Chunking**: Preserve meaning across chunks
3. **Compare Vector Databases**: Hands-on experience with production systems
4. **Optimize Indexing**: Trade-offs between speed and accuracy
5. **Implement Re-ranking**: Improve retrieval relevance
6. **Build Production RAG**: End-to-end system development

## 📚 Resources

### Vector Databases
- **MongoDB Atlas**: [Vector Search Documentation](https://docs.atlas.mongodb.com/atlas-vector-search/)
- **AstraDB**: [Vector Search Guide](https://docs.datastax.com/en/astra-serverless/docs/vector-search/)
- **OpenSearch**: [k-NN Plugin](https://opensearch.org/docs/latest/search-plugins/knn/)
- **Milvus**: [Getting Started](https://milvus.io/docs/quickstart.md)

### Indexing Algorithms
- **HNSW**: Hierarchical Navigable Small World graphs
- **IVF**: Inverted File indexing for large-scale search
- **Flat**: Brute-force exact search baseline

### Re-ranking Methods
- **BM25**: Probabilistic ranking function
- **MMR**: Maximal Marginal Relevance for diversity

## 🏆 Submission Requirements

### GitHub Repository
- [ ] Complete code with documentation
- [ ] All notebooks with outputs
- [ ] Performance comparison results
- [ ] Sample DOCX outputs

### Email Submission
- **To**: <EMAIL>
- **Subject**: Advanced PDF RAG Assignment - [Your Name]
- **Include**: GitHub repository link

### Community Sharing
- [ ] Share in community chat
- [ ] Tag @krish and @sunny
- [ ] Include key learnings and results

## ⏰ Deadline
**Friday 9PM** - No extensions!

## 💡 Pro Tips

1. **Start Early**: This is a complex assignment
2. **Test Incrementally**: Validate each component
3. **Document Everything**: Clear explanations help
4. **Optimize Iteratively**: Start simple, then improve
5. **Ask Questions**: Use community for help

**Good luck! 🚀**
