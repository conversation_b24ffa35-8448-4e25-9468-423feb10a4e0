# ===============================================================================
# 🐍 CONDA ENVIRONMENT CONFIGURATION
# ===============================================================================
# Vector Database Learning Project - Conda Environment
# 
# USAGE:
#   conda env create -f environment.yml
#   conda activate vector-db-learning
#
# ===============================================================================

name: vector-db-learning

channels:
  - conda-forge
  - defaults

dependencies:
  # Python version
  - python=3.9

  # Core scientific computing
  - numpy=1.24.3
  - pandas=2.0.3
  - scipy=1.11.1
  - scikit-learn=1.3.0

  # Visualization
  - matplotlib=3.7.2
  - seaborn=0.12.2
  - plotly=5.15.0

  # Jupyter ecosystem
  - jupyter=1.0.0
  - jupyterlab=4.0.5
  - ipykernel=6.25.0
  - ipywidgets=8.1.0

  # Utilities
  - requests=2.31.0
  - tqdm=4.66.1
  - python-dotenv=1.0.0
  - beautifulsoup4=4.12.2

  # Development tools
  - pytest=7.4.0
  - black=23.7.0
  - flake8=6.0.0

  # Pip packages (AI/ML libraries work better with pip)
  - pip=23.2.1
  - pip:
    # LangChain ecosystem
    - langchain==0.1.20
    - langchain-community==0.0.38
    - langchain-core==0.1.52
    - langchain-text-splitters==0.0.1
    - langchain-huggingface==0.0.3
    - langchain-google-genai==1.0.6
    - langchain-pinecone==0.1.1
    - langchain-chroma==0.1.1
    - langchain-openai==0.1.8

    # Vector databases
    - faiss-cpu==1.7.4
    - pinecone-client==3.2.2
    - chromadb==0.4.24

    # Embeddings and models
    - sentence-transformers>=2.6.0
    - transformers>=4.33.2
    - torch>=2.0.1
    - huggingface-hub>=0.23.0

    # API clients
    - openai==1.35.5
    - google-generativeai==0.5.4

    # Additional utilities
    - pypdf==3.15.2
    - python-multipart==0.0.6
    - streamlit==1.25.0

# ===============================================================================
# 📝 INSTALLATION INSTRUCTIONS
# ===============================================================================
#
# 🚀 QUICK START:
#   conda env create -f environment.yml
#   conda activate vector-db-learning
#   jupyter notebook
#
# 🔧 ALTERNATIVE METHODS:
#
# Method 1: Using this file
#   conda env create -f environment.yml
#
# Method 2: Using setup script
#   python setup_conda.py
#
# Method 3: Manual creation
#   conda create -n vector-db-learning python=3.9
#   conda activate vector-db-learning
#   pip install -r requirements.txt
#
# ===============================================================================
# 🛠️ ENVIRONMENT MANAGEMENT
# ===============================================================================
#
# Activate environment:
#   conda activate vector-db-learning
#
# Deactivate environment:
#   conda deactivate
#
# Update environment:
#   conda env update -f environment.yml
#
# Export current environment:
#   conda env export > environment_backup.yml
#
# Remove environment:
#   conda env remove -n vector-db-learning
#
# List all environments:
#   conda env list
#
# ===============================================================================
# 🔍 PACKAGE SELECTION RATIONALE
# ===============================================================================
#
# CONDA PACKAGES (Faster, better dependency resolution):
#   • Core scientific: numpy, pandas, scipy, scikit-learn
#   • Visualization: matplotlib, seaborn, plotly
#   • Jupyter: jupyter, jupyterlab, ipykernel
#   • Utilities: requests, tqdm, python-dotenv
#
# PIP PACKAGES (Latest versions, AI/ML focus):
#   • LangChain: Latest framework for LLM applications
#   • Vector DBs: FAISS, Pinecone, ChromaDB
#   • ML Models: transformers, sentence-transformers, torch
#   • API Clients: openai, google-generativeai
#
# ===============================================================================
# 🎯 OPTIMIZATIONS FOR LEARNING
# ===============================================================================
#
# • Python 3.9: Stable, compatible with all packages
# • CPU-only PyTorch: Faster installation, sufficient for learning
# • Pinned versions: Ensures reproducibility across systems
# • Conda-forge channel: More up-to-date packages
# • Mixed conda/pip: Best of both package managers
#
# ===============================================================================
