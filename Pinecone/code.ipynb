from dotenv import load_dotenv
load_dotenv()

import os
os.environ['HF_TOKEN']=os.getenv("HF_TOKEN")

from langchain_huggingface import HuggingFaceEmbeddings
embeddings=HuggingFaceEmbeddings(model_name="all-MiniLM-L6-v2")

embeddings.embed_query("hello AI")

len(embeddings.embed_query("hello AI"))

from langchain_google_genai import GoogleGenerativeAIEmbeddings
embeddings = GoogleGenerativeAIEmbeddings(model="models/embedding-001")

embeddings.embed_query("Hello AI")

len(embeddings.embed_query("Hello AI"))

from pinecone import Pinecone

import os
pinecone_api_key=os.getenv("PINECONE_API_KEY")

pc=Pinecone(api_key=pinecone_api_key)

from pinecone import ServerlessSpec
#Serverless: Server will be Managed by the cloud provider

index_name="agenticbatch2"

pc.has_index(index_name)

pc.has_index("trading-bot")

#creating a index
if not pc.has_index(index_name):
    pc.create_index(
    name=index_name,
    dimension=768,
    metric="cosine",
    spec=ServerlessSpec(cloud="aws",region="us-east-1")    
)

#loading the index
index=pc.Index(index_name)

from langchain_pinecone import PineconeVectorStore

vector_store=PineconeVectorStore(index=index,embedding=embeddings)

results = vector_store.similarity_search("what is a langchain?")

results

from uuid import uuid4
from langchain_core.documents import Document

document_1 = Document(
    page_content="I had chocolate chip pancakes and scrambled eggs for breakfast this morning.",
    metadata={"source": "tweet"},#additional info
)

document_2 = Document(
    page_content="The weather forecast for tomorrow is cloudy and overcast, with a high of 62 degrees.",
    metadata={"source": "news"},
)

document_3 = Document(
    page_content="Building an exciting new project with LangChain - come check it out!",
    metadata={"source": "tweet"},
)

document_4 = Document(
    page_content="Robbers broke into the city bank and stole $1 million in cash.",
    metadata={"source": "news"},
)

document_5 = Document(
    page_content="Wow! That was an amazing movie. I can't wait to see it again.",
    metadata={"source": "tweet"},
)

document_6 = Document(
    page_content="Is the new iPhone worth the price? Read this review to find out.",
    metadata={"source": "website"},
)

document_7 = Document(
    page_content="The top 10 soccer players in the world right now.",
    metadata={"source": "website"},
)

document_8 = Document(
    page_content="LangGraph is the best framework for building stateful, agentic applications!",
    metadata={"source": "tweet"},
)

document_9 = Document(
    page_content="The stock market is down 500 points today due to fears of a recession.",
    metadata={"source": "news"},
)

document_10 = Document(
    page_content="I have a bad feeling I am going to get deleted :(",
    metadata={"source": "tweet"},
)


documents = [
    document_1,
    document_2,
    document_3,
    document_4,
    document_5,
    document_6,
    document_7,
    document_8,
    document_9,
    document_10,
]

documents

len(documents)

range(len(documents))

for _ in range(len(documents)):
    print(_)
    print(str(uuid4()))
    

#universal indentification number
uuids = [str(uuid4()) for _ in range(len(documents))]

uuids

vector_store.add_documents(documents=documents, ids=uuids)

results = vector_store.similarity_search("what langchain provides to us?",k=1)

results

results = vector_store.similarity_search("what langchain provides to us?",filter={"source": "tweet"})

results

retriever=vector_store.as_retriever(
    search_type="similarity_score_threshold",
    search_kwargs={"score_threshold": 0.7} #hyperparameter
)

retriever.invoke("langchain")

retriever.invoke("google")

from langchain_google_genai import ChatGoogleGenerativeAI
model=ChatGoogleGenerativeAI(model='gemini-1.5-flash')

from langchain import hub
prompt = hub.pull("rlm/rag-prompt")

import pprint
pprint.pprint(prompt.messages)

from langchain_core.prompts import PromptTemplate

prompt=PromptTemplate(
    template="""You are an assistant for question-answering tasks. Use the following pieces of retrieved context to answer the question. If you don't know the answer, just say that you don't know. Use three sentences maximum and keep the answer concise.\nQuestion: {question} \nContext: {context} \nAnswer:""",
    input_variables=['context', 'question']
)

prompt

prompt.invoke({"question":"what is a langchain?","context":"langchain is very super framework for LLM."})

from langchain_core.output_parsers import StrOutputParser
from langchain_core.runnables import RunnablePassthrough

def format_docs(docs):
    return "\n\n".join(doc.page_content for doc in docs)

rag_chain = (
    {"context": retriever | format_docs, "question": RunnablePassthrough()}
    | prompt
    | model
    | StrOutputParser()
)

rag_chain.invoke("what is llama model?")

# 📊 Visualize Vector Similarities
# Create a similarity matrix and visualize it

vector_names = list(vectors.keys())
n_vectors = len(vector_names)

# Create similarity matrix
similarity_matrix = np.zeros((n_vectors, n_vectors))

for i, name1 in enumerate(vector_names):
    for j, name2 in enumerate(vector_names):
        similarity_matrix[i][j] = calculate_cosine_similarity(vectors[name1], vectors[name2])

# Plot heatmap
plt.figure(figsize=(10, 8))
sns.heatmap(similarity_matrix, 
            xticklabels=vector_names, 
            yticklabels=vector_names,
            annot=True, 
            cmap='RdYlBu_r',
            center=0.5,
            square=True,
            fmt='.3f')
plt.title('🔥 Cosine Similarity Matrix\n(Darker = More Similar)', fontsize=14, fontweight='bold')
plt.xlabel('Concepts', fontweight='bold')
plt.ylabel('Concepts', fontweight='bold')
plt.tight_layout()
plt.show()

print("\n💡 Insights:")
print("• 'cat' and 'kitten' are most similar (both felines)")
print("• 'car' and 'vehicle' are highly similar (both transportation)")
print("• Animals vs vehicles show low similarity")

# 📊 Visualize Vector Similarities
# Create a similarity matrix and visualize it

vector_names = list(vectors.keys())
n_vectors = len(vector_names)

# Create similarity matrix
similarity_matrix = np.zeros((n_vectors, n_vectors))

for i, name1 in enumerate(vector_names):
    for j, name2 in enumerate(vector_names):
        similarity_matrix[i][j] = calculate_cosine_similarity(vectors[name1], vectors[name2])

# Plot heatmap
plt.figure(figsize=(10, 8))
sns.heatmap(similarity_matrix, 
            xticklabels=vector_names, 
            yticklabels=vector_names,
            annot=True, 
            cmap='RdYlBu_r',
            center=0.5,
            square=True,
            fmt='.3f')
plt.title('🔥 Cosine Similarity Matrix\n(Darker = More Similar)', fontsize=14, fontweight='bold')
plt.xlabel('Concepts', fontweight='bold')
plt.ylabel('Concepts', fontweight='bold')
plt.tight_layout()
plt.show()

print("\n💡 Insights:")
print("• 'cat' and 'kitten' are most similar (both felines)")
print("• 'car' and 'vehicle' are highly similar (both transportation)")
print("• Animals vs vehicles show low similarity")

# 📊 Visualize Vector Similarities
# Create a similarity matrix and visualize it

vector_names = list(vectors.keys())
n_vectors = len(vector_names)

# Create similarity matrix
similarity_matrix = np.zeros((n_vectors, n_vectors))

for i, name1 in enumerate(vector_names):
    for j, name2 in enumerate(vector_names):
        similarity_matrix[i][j] = calculate_cosine_similarity(vectors[name1], vectors[name2])

# Plot heatmap
plt.figure(figsize=(10, 8))
sns.heatmap(similarity_matrix, 
            xticklabels=vector_names, 
            yticklabels=vector_names,
            annot=True, 
            cmap='RdYlBu_r',
            center=0.5,
            square=True,
            fmt='.3f')
plt.title('🔥 Cosine Similarity Matrix\n(Darker = More Similar)', fontsize=14, fontweight='bold')
plt.xlabel('Concepts', fontweight='bold')
plt.ylabel('Concepts', fontweight='bold')
plt.tight_layout()
plt.show()

print("\n💡 Insights:")
print("• 'cat' and 'kitten' are most similar (both felines)")
print("• 'car' and 'vehicle' are highly similar (both transportation)")
print("• Animals vs vehicles show low similarity")

# 📊 Visualize Vector Similarities
# Create a similarity matrix and visualize it

vector_names = list(vectors.keys())
n_vectors = len(vector_names)

# Create similarity matrix
similarity_matrix = np.zeros((n_vectors, n_vectors))

for i, name1 in enumerate(vector_names):
    for j, name2 in enumerate(vector_names):
        similarity_matrix[i][j] = calculate_cosine_similarity(vectors[name1], vectors[name2])

# Plot heatmap
plt.figure(figsize=(10, 8))
sns.heatmap(similarity_matrix, 
            xticklabels=vector_names, 
            yticklabels=vector_names,
            annot=True, 
            cmap='RdYlBu_r',
            center=0.5,
            square=True,
            fmt='.3f')
plt.title('🔥 Cosine Similarity Matrix\n(Darker = More Similar)', fontsize=14, fontweight='bold')
plt.xlabel('Concepts', fontweight='bold')
plt.ylabel('Concepts', fontweight='bold')
plt.tight_layout()
plt.show()

print("\n💡 Insights:")
print("• 'cat' and 'kitten' are most similar (both felines)")
print("• 'car' and 'vehicle' are highly similar (both transportation)")
print("• Animals vs vehicles show low similarity")