@echo off
REM ===============================================================================
REM 🐍 Vector Database Learning Project - Conda Setup Script (Windows)
REM ===============================================================================

echo 🚀 Vector Database Learning Project - Conda Setup
echo ==================================================
echo.

set ENV_NAME=vector-db-learning

REM Check if conda is installed
echo 🔍 Checking conda installation...
conda --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Conda not found!
    echo 💡 Please install Anaconda or Miniconda:
    echo    • Anaconda: https://www.anaconda.com/download
    echo    • Miniconda: https://docs.conda.io/en/latest/miniconda.html
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('conda --version') do set CONDA_VERSION=%%i
echo ✅ Found %CONDA_VERSION%

REM Check if environment already exists
echo.
echo 🏗️ Checking if environment '%ENV_NAME%' exists...
conda env list | findstr /C:"%ENV_NAME%" >nul
if %errorlevel% equ 0 (
    echo ⚠️ Environment '%ENV_NAME%' already exists
    set /p UPDATE="🔄 Do you want to update it? (y/n): "
    if /i "%UPDATE%"=="y" (
        echo 🔄 Updating environment...
        conda env update -f environment.yml
    ) else (
        echo 📋 Using existing environment
    )
) else (
    echo 🆕 Creating new environment '%ENV_NAME%'...
    conda env create -f environment.yml
)

if %errorlevel% neq 0 (
    echo ❌ Environment setup failed!
    pause
    exit /b 1
)

echo ✅ Environment setup completed!

REM Setup Jupyter kernel
echo.
echo 📓 Setting up Jupyter kernel...
conda run -n %ENV_NAME% python -m ipykernel install --user --name %ENV_NAME% --display-name "Python (vector-db-learning)"

if %errorlevel% equ 0 (
    echo ✅ Jupyter kernel installed!
) else (
    echo ⚠️ Jupyter kernel setup failed (you can do this manually later)
)

REM Setup .env file
echo.
echo 🔑 Setting up environment file...
if not exist ".env" (
    if exist ".env.template" (
        copy .env.template .env >nul
        echo ✅ .env file created from template
        echo ℹ️ Please edit .env file and add your API keys
    ) else (
        echo ⚠️ .env.template not found
    )
) else (
    echo ✅ .env file already exists
)

REM Print next steps
echo.
echo 🎉 Setup Complete! Next Steps:
echo ==============================
echo.
echo 1. Activate your conda environment:
echo    conda activate %ENV_NAME%
echo.
echo 2. Edit your .env file with API keys:
echo    • HF_TOKEN (HuggingFace - Free)
echo    • OPENAI_API_KEY (OpenAI - Paid)
echo    • GOOGLE_API_KEY (Google AI - Free tier)
echo    • PINECONE_API_KEY (Pinecone - Free tier)
echo.
echo 3. Start Jupyter Notebook:
echo    jupyter notebook
echo.
echo 4. Open the first notebook:
echo    01_Vector_Database_Fundamentals.ipynb
echo.
echo 📚 Learning Path:
echo 1. 01_Vector_Database_Fundamentals.ipynb
echo 2. 02_Embeddings_and_Models.ipynb
echo 3. 03_FAISS_Implementation.ipynb
echo 4. 04_Pinecone_Cloud_Database.ipynb
echo 5. 05_RAG_Implementation.ipynb
echo 6. 06_Production_Deployment.ipynb
echo.
echo Happy Learning! 🐍🚀
echo.
pause
