{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# 🤖 RAG (Retrieval Augmented Generation) Implementation\n",
    "\n",
    "## 📚 Table of Contents\n",
    "1. [What is RAG?](#what-is-rag)\n",
    "2. [RAG Architecture](#rag-architecture)\n",
    "3. [Document Processing Pipeline](#document-processing-pipeline)\n",
    "4. [Vector Store Setup](#vector-store-setup)\n",
    "5. [Retrieval System](#retrieval-system)\n",
    "6. [Generation with LLMs](#generation-with-llms)\n",
    "7. [Complete RAG Pipeline](#complete-rag-pipeline)\n",
    "8. [Advanced RAG Techniques](#advanced-rag-techniques)\n",
    "\n",
    "---\n",
    "\n",
    "## 🎯 Learning Objectives\n",
    "By the end of this notebook, you will:\n",
    "- Understand RAG architecture and components\n",
    "- Build a complete document processing pipeline\n",
    "- Implement retrieval systems with vector databases\n",
    "- Integrate LLMs for answer generation\n",
    "- Create production-ready RAG applications\n",
    "- Apply advanced RAG optimization techniques"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 🧠 What is RAG?\n",
    "\n",
    "**RAG (Retrieval Augmented Generation)** combines information retrieval with text generation to provide accurate, contextual answers.\n",
    "\n",
    "### 🎯 Key Benefits:\n",
    "- **Accuracy**: Grounds responses in factual information\n",
    "- **Freshness**: Uses up-to-date information from documents\n",
    "- **Transparency**: Shows sources for answers\n",
    "- **Cost-Effective**: Avoids fine-tuning large models\n",
    "- **Domain-Specific**: Works with specialized knowledge\n",
    "\n",
    "### 🔄 RAG vs Traditional Approaches:\n",
    "\n",
    "| Approach | Knowledge Source | Updates | Accuracy | Cost |\n",
    "|----------|------------------|---------|----------|------|\n",
    "| **Pre-trained LLM** | Training data | Static | Medium | Low |\n",
    "| **Fine-tuned LLM** | Custom training | Expensive | High | Very High |\n",
    "| **RAG** | External docs | Real-time | High | Medium |\n",
    "\n",
    "### 🏗️ RAG Architecture:\n",
    "```\n",
    "Documents → Chunking → Embeddings → Vector Store\n",
    "                                         ↓\n",
    "User Query → Embedding → Similarity Search → Retrieved Docs\n",
    "                                         ↓\n",
    "Query + Context → LLM → Generated Answer\n",
    "```"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 🛠️ Setup and Imports\n",
    "import numpy as np\n",
    "import pandas as pd\n",
    "import matplotlib.pyplot as plt\n",
    "import seaborn as sns\n",
    "import time\n",
    "import warnings\n",
    "warnings.filterwarnings('ignore')\n",
    "\n",
    "# Load environment variables\n",
    "from dotenv import load_dotenv\n",
    "import os\n",
    "load_dotenv()\n",
    "\n",
    "# Set up API keys\n",
    "os.environ['HF_TOKEN'] = os.getenv(\"HF_TOKEN\", \"\")\n",
    "os.environ['OPENAI_API_KEY'] = os.getenv(\"OPENAI_API_KEY\", \"\")\n",
    "os.environ['GOOGLE_API_KEY'] = os.getenv(\"GOOGLE_API_KEY\", \"\")\n",
    "\n",
    "# LangChain imports\n",
    "from langchain_core.documents import Document\n",
    "from langchain_text_splitters import RecursiveCharacterTextSplitter\n",
    "from langchain_huggingface import HuggingFaceEmbeddings\n",
    "from langchain_community.vectorstores import FAISS\n",
    "from langchain_core.prompts import ChatPromptTemplate\n",
    "from langchain_core.runnables import RunnablePassthrough\n",
    "from langchain_core.output_parsers import StrOutputParser\n",
    "\n",
    "print(\"✅ Basic imports successful!\")\n",
    "print(\"🔑 Checking API keys...\")\n",
    "print(f\"   HF_TOKEN: {'✅' if os.getenv('HF_TOKEN') else '❌'}\")\n",
    "print(f\"   OPENAI_API_KEY: {'✅' if os.getenv('OPENAI_API_KEY') else '❌'}\")\n",
    "print(f\"   GOOGLE_API_KEY: {'✅' if os.getenv('GOOGLE_API_KEY') else '❌'}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 🤖 Setup LLM (Language Model)\n",
    "llm_available = False\n",
    "llm = None\n",
    "\n",
    "# Try OpenAI first\n",
    "if os.getenv('OPENAI_API_KEY'):\n",
    "    try:\n",
    "        from langchain_openai import ChatOpenAI\n",
    "        llm = ChatOpenAI(\n",
    "            model=\"gpt-3.5-turbo\",\n",
    "            temperature=0,\n",
    "            openai_api_key=os.getenv('OPENAI_API_KEY')\n",
    "        )\n",
    "        llm_available = True\n",
    "        print(\"✅ OpenAI ChatGPT loaded successfully!\")\n",
    "    except Exception as e:\n",
    "        print(f\"❌ Failed to load OpenAI: {e}\")\n",
    "\n",
    "# Try Google AI if OpenAI failed\n",
    "if not llm_available and os.getenv('GOOGLE_API_KEY'):\n",
    "    try:\n",
    "        from langchain_google_genai import ChatGoogleGenerativeAI\n",
    "        llm = ChatGoogleGenerativeAI(\n",
    "            model=\"gemini-pro\",\n",
    "            temperature=0,\n",
    "            google_api_key=os.getenv('GOOGLE_API_KEY')\n",
    "        )\n",
    "        llm_available = True\n",
    "        print(\"✅ Google Gemini loaded successfully!\")\n",
    "    except Exception as e:\n",
    "        print(f\"❌ Failed to load Google AI: {e}\")\n",
    "\n",
    "if not llm_available:\n",
    "    print(\"⚠️ No LLM available!\")\n",
    "    print(\"💡 To use RAG with generation:\")\n",
    "    print(\"   1. Add OPENAI_API_KEY or GOOGLE_API_KEY to .env\")\n",
    "    print(\"   2. Restart the notebook\")\n",
    "    print(\"   3. For now, we'll focus on the retrieval part\")\n",
    "else:\n",
    "    print(\"🚀 Ready for full RAG implementation!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 📚 Create Sample Knowledge Base\n",
    "# This simulates a company's internal documentation\n",
    "\n",
    "knowledge_base = [\n",
    "    {\n",
    "        \"title\": \"Vector Database Overview\",\n",
    "        \"content\": \"\"\"Vector databases are specialized databases designed to store and query high-dimensional vectors efficiently. \n",
    "        They are essential for AI applications like semantic search, recommendation systems, and retrieval-augmented generation (RAG). \n",
    "        Popular vector databases include Pinecone, Weaviate, Qdrant, and FAISS. These databases use advanced indexing \n",
    "        techniques like HNSW, IVF, and LSH to enable fast similarity search across millions or billions of vectors.\"\"\",\n",
    "        \"category\": \"technology\",\n",
    "        \"tags\": [\"vectors\", \"databases\", \"AI\", \"search\"]\n",
    "    },\n",
    "    {\n",
    "        \"title\": \"Embedding Models Explained\",\n",
    "        \"content\": \"\"\"Embedding models convert text, images, or other data into numerical vectors that capture semantic meaning. \n",
    "        Popular text embedding models include OpenAI's text-embedding-ada-002, Google's Universal Sentence Encoder, \n",
    "        and Hugging Face's sentence-transformers. The choice of embedding model significantly impacts the quality \n",
    "        of semantic search results. Models are typically evaluated on tasks like semantic textual similarity and \n",
    "        information retrieval benchmarks.\"\"\",\n",
    "        \"category\": \"AI\",\n",
    "        \"tags\": [\"embeddings\", \"models\", \"NLP\", \"semantic\"]\n",
    "    },\n",
    "    {\n",
    "        \"title\": \"RAG Implementation Best Practices\",\n",
    "        \"content\": \"\"\"Retrieval-Augmented Generation (RAG) combines information retrieval with text generation to provide \n",
    "        accurate, contextual answers. Key best practices include: 1) Proper document chunking with overlap, \n",
    "        2) Using high-quality embedding models, 3) Implementing hybrid search (dense + sparse), \n",
    "        4) Optimizing retrieval parameters, 5) Prompt engineering for better generation, \n",
    "        6) Implementing evaluation metrics, and 7) Handling edge cases and fallbacks.\"\"\",\n",
    "        \"category\": \"AI\",\n",
    "        \"tags\": [\"RAG\", \"retrieval\", \"generation\", \"best-practices\"]\n",
    "    },\n",
    "    {\n",
    "        \"title\": \"FAISS Performance Optimization\",\n",
    "        \"content\": \"\"\"FAISS (Facebook AI Similarity Search) offers various index types for different use cases. \n",
    "        IndexFlatL2 provides exact search but is slow for large datasets. IndexIVFFlat offers good \n",
    "        speed-accuracy tradeoff for medium datasets. IndexIVFPQ uses product quantization for \n",
    "        memory efficiency with large datasets. IndexHNSW provides excellent recall but uses more memory. \n",
    "        Choose index type based on dataset size, memory constraints, and accuracy requirements.\"\"\",\n",
    "        \"category\": \"technology\",\n",
    "        \"tags\": [\"FAISS\", \"performance\", \"optimization\", \"indexing\"]\n",
    "    },\n",
    "    {\n",
    "        \"title\": \"Pinecone Production Deployment\",\n",
    "        \"content\": \"\"\"Pinecone is a managed vector database service ideal for production deployments. \n",
    "        Key considerations include: choosing the right pod type and size, configuring replicas \n",
    "        for high availability, implementing proper error handling and retries, monitoring \n",
    "        query latency and throughput, managing costs through efficient indexing, and \n",
    "        implementing security best practices including API key management and network security.\"\"\",\n",
    "        \"category\": \"technology\",\n",
    "        \"tags\": [\"Pinecone\", \"production\", \"deployment\", \"cloud\"]\n",
    "    }\n",
    "]\n",
    "\n",
    "print(f\"📚 Created knowledge base with {len(knowledge_base)} documents\")\n",
    "print(\"\\n📄 Sample document:\")\n",
    "print(f\"Title: {knowledge_base[0]['title']}\")\n",
    "print(f\"Content: {knowledge_base[0]['content'][:100]}...\")\n",
    "print(f\"Category: {knowledge_base[0]['category']}\")\n",
    "print(f\"Tags: {knowledge_base[0]['tags']}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 🔪 Step 4.1: Document Chunking Strategy\n",
    "# Why chunking? Large documents need to be broken into smaller pieces for better search\n",
    "\n",
    "print(\"🔪 Setting up document chunking...\")\n",
    "print(\"\\n🎯 Why we need chunking:\")\n",
    "print(\"   • Large documents are hard to search through\")\n",
    "print(\"   • Smaller chunks = more precise search results\")\n",
    "print(\"   • Each chunk becomes a searchable unit\")\n",
    "\n",
    "# Initialize text splitter\n",
    "text_splitter = RecursiveCharacterTextSplitter(\n",
    "    chunk_size=500,        # Size of each chunk (characters)\n",
    "    chunk_overlap=50,      # Overlap between chunks (prevents cutting sentences)\n",
    "    length_function=len,   # How to measure length\n",
    "    separators=[\"\\n\\n\", \"\\n\", \".\", \" \", \"\"]  # Split at paragraphs first, then sentences\n",
    ")\n",
    "\n",
    "print(\"\\n⚙️ Chunking settings:\")\n",
    "print(\"   • Chunk size: 500 characters (about 1-2 paragraphs)\")\n",
    "print(\"   • Overlap: 50 characters (prevents losing context)\")\n",
    "print(\"   • Smart splitting: Tries to keep sentences together\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 🔄 Step 4.2: Convert Knowledge Base to Documents\n",
    "# Transform our data into LangChain document format\n",
    "\n",
    "documents = []\n",
    "print(\"📄 Converting knowledge base to documents...\")\n",
    "\n",
    "for i, item in enumerate(knowledge_base, 1):\n",
    "    doc = Document(\n",
    "        page_content=item['content'],  # The actual text content\n",
    "        metadata={                     # Additional information about the document\n",
    "            'title': item['title'],\n",
    "            'category': item['category'],\n",
    "            'tags': item['tags'],\n",
    "            'doc_id': i\n",
    "        }\n",
    "    )\n",
    "    documents.append(doc)\n",
    "    print(f\"   {i}. {item['title']} ({len(item['content'])} characters)\")\n",
    "\n",
    "print(f\"\\n✅ Created {len(documents)} documents\")\n",
    "print(\"💡 Each document now has content + metadata for better search\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# ✂️ Step 4.3: Split Documents into Chunks\n",
    "# Break large documents into smaller, searchable pieces\n",
    "\n",
    "print(\"✂️ Splitting documents into chunks...\")\n",
    "chunks = text_splitter.split_documents(documents)\n",
    "\n",
    "print(f\"✅ Created {len(chunks)} chunks from {len(documents)} documents\")\n",
    "print(f\"📊 Average chunk size: {np.mean([len(chunk.page_content) for chunk in chunks]):.0f} characters\")\n",
    "\n",
    "# Show chunk distribution\n",
    "chunk_sizes = [len(chunk.page_content) for chunk in chunks]\n",
    "print(f\"📏 Chunk size range: {min(chunk_sizes)} - {max(chunk_sizes)} characters\")\n",
    "\n",
    "# Show sample chunk\n",
    "print(\"\\n📄 Sample chunk:\")\n",
    "print(f\"Content: {chunks[0].page_content[:200]}...\")\n",
    "print(f\"Metadata: {chunks[0].metadata}\")\n",
    "\n",
    "print(\"\\n💡 Why this works:\")\n",
    "print(\"   • Each chunk is small enough for focused search\")\n",
    "print(\"   • Overlap ensures we don't lose context at boundaries\")\n",
    "print(\"   • Metadata helps us track which document each chunk came from\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 🗄️ Step 5: Building the Vector Store\n",
    "\n",
    "### 🎯 What is a Vector Store?\n",
    "A **vector store** is like a smart library that:\n",
    "- **Stores** all our document chunks as embeddings (numbers)\n",
    "- **Indexes** them for fast searching\n",
    "- **Retrieves** the most relevant chunks for any query\n",
    "\n",
    "### 🔍 How Vector Search Works:\n",
    "1. **Query comes in**: \"Tell me about machine learning\"\n",
    "2. **Convert to embedding**: Query becomes numbers\n",
    "3. **Search vector store**: Find similar number patterns\n",
    "4. **Return relevant chunks**: Documents about ML\n",
    "\n",
    "### 🚀 Why This is Powerful:\n",
    "- **Semantic understanding**: Finds meaning, not just keywords\n",
    "- **Fast retrieval**: Optimized for speed\n",
    "- **Scalable**: Works with millions of documents"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 🚀 Step 5.1: Load Embedding Model for Vector Store\n",
    "from langchain_huggingface import HuggingFaceEmbeddings\n",
    "from langchain_community.vectorstores import FAISS\n",
    "\n",
    "print(\"🤖 Loading embedding model for vector store...\")\n",
    "\n",
    "# Initialize embeddings (same as before, but for vector store)\n",
    "embeddings = HuggingFaceEmbeddings(\n",
    "    model_name=\"all-MiniLM-L6-v2\",\n",
    "    model_kwargs={'device': 'cpu'},\n",
    "    encode_kwargs={'normalize_embeddings': True}\n",
    ")\n",
    "\n",
    "print(\"✅ Embedding model loaded!\")\n",
    "print(\"🎯 What this model will do:\")\n",
    "print(\"   • Convert each chunk into 384 numbers\")\n",
    "print(\"   • Convert search queries into same format\")\n",
    "print(\"   • Enable semantic similarity search\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 🏗️ Step 5.2: Create Vector Store from Chunks\n",
    "# This is where the magic happens - converting text to searchable vectors!\n",
    "\n",
    "print(\"🏗️ Creating vector store from document chunks...\")\n",
    "print(\"⏳ This may take a moment to process all chunks...\")\n",
    "\n",
    "# Create FAISS vector store from our chunks\n",
    "vector_store = FAISS.from_documents(\n",
    "    documents=chunks,      # Our chunked documents\n",
    "    embedding=embeddings   # The embedding model\n",
    ")\n",
    "\n",
    "print(f\"✅ Vector store created!\")\n",
    "print(f\"📊 Stored {len(chunks)} document chunks as vectors\")\n",
    "print(f\"🔍 Ready for semantic search!\")\n",
    "\n",
    "print(\"\\n💡 What just happened:\")\n",
    "print(\"   • Each chunk was converted to 384 numbers\")\n",
    "print(\"   • FAISS indexed all vectors for fast search\")\n",
    "print(\"   • We can now find similar content instantly\")"
   ]
  },
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },\n",
  "language_info": {\n",
   "codemirror_mode": {\n",
    "name": "ipython",\n",
    "version": 3\n",
   },\n",
   "file_extension": ".py",\n",
   "mimetype": "text/x-python",\n",
   "name": "python",\n",
   "nbconvert_exporter": "python",\n",
   "pygments_lexer": "ipython3",\n",
   "version": "3.8.5"\n",
  }\n",
 },\n",
 "nbformat": 4,\n",
 "nbformat_minor": 4\n}
