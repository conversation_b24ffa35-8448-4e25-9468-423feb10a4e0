# 🛠️ Production-Ready Setup
import numpy as np
import pandas as pd
import time
import logging
import json
import asyncio
import concurrent.futures
from datetime import datetime
from typing import List, Dict, Optional, Tuple
import warnings
warnings.filterwarnings('ignore')

# Load environment variables
from dotenv import load_dotenv
import os
load_dotenv()

# Production imports
from langchain_huggingface import HuggingFaceEmbeddings
from langchain_community.vectorstores import FAISS
from langchain_core.documents import Document

print("✅ Production setup imports complete!")
print("🏭 Ready for production deployment patterns")

# 📊 Performance Monitoring Class
class PerformanceMonitor:
    """Monitor and track performance metrics"""
    
    def __init__(self):
        self.metrics = {
            'search_latency': [],
            'embedding_latency': [],
            'total_requests': 0,
            'error_count': 0,
            'cache_hits': 0,
            'cache_misses': 0
        }
        
    def record_search_latency(self, latency: float):
        """Record search operation latency"""
        self.metrics['search_latency'].append(latency)
        
    def record_embedding_latency(self, latency: float):
        """Record embedding generation latency"""
        self.metrics['embedding_latency'].append(latency)
        
    def increment_requests(self):
        """Increment total request counter"""
        self.metrics['total_requests'] += 1
        
    def increment_errors(self):
        """Increment error counter"""
        self.metrics['error_count'] += 1
        
    def record_cache_hit(self):
        """Record cache hit"""
        self.metrics['cache_hits'] += 1
        
    def record_cache_miss(self):
        """Record cache miss"""
        self.metrics['cache_misses'] += 1
        
    def get_stats(self) -> Dict:
        """Get performance statistics"""
        search_latencies = self.metrics['search_latency']
        embedding_latencies = self.metrics['embedding_latency']
        
        stats = {
            'total_requests': self.metrics['total_requests'],
            'error_rate': self.metrics['error_count'] / max(1, self.metrics['total_requests']),
            'cache_hit_rate': self.metrics['cache_hits'] / max(1, self.metrics['cache_hits'] + self.metrics['cache_misses'])
        }
        
        if search_latencies:
            stats.update({
                'avg_search_latency': np.mean(search_latencies),
                'p95_search_latency': np.percentile(search_latencies, 95),
                'p99_search_latency': np.percentile(search_latencies, 99)
            })
            
        if embedding_latencies:
            stats.update({
                'avg_embedding_latency': np.mean(embedding_latencies),
                'p95_embedding_latency': np.percentile(embedding_latencies, 95)
            })
            
        return stats
    
    def reset_metrics(self):
        """Reset all metrics"""
        self.__init__()

print("✅ Performance monitoring class created!")

# 🔧 Production Vector Search Service
class ProductionVectorSearch:
    """Production-ready vector search service with caching, monitoring, and error handling"""
    
    def __init__(self, embedding_model_name: str = "all-MiniLM-L6-v2"):
        # Initialize components
        self.embeddings = HuggingFaceEmbeddings(
            model_name=embedding_model_name,
            model_kwargs={'device': 'cpu'},
            encode_kwargs={'normalize_embeddings': True}
        )
        
        self.vector_store = None
        self.monitor = PerformanceMonitor()
        self.cache = {}  # Simple in-memory cache
        self.cache_ttl = 300  # 5 minutes
        
        # Setup logging
        self.logger = self._setup_logging()
        
        self.logger.info("ProductionVectorSearch initialized")
        
    def _setup_logging(self) -> logging.Logger:
        """Setup structured logging"""
        logger = logging.getLogger('VectorSearch')
        logger.setLevel(logging.INFO)
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        return logger
    
    def load_documents(self, documents: List[Document]):
        """Load documents into vector store"""
        try:
            start_time = time.time()
            
            self.vector_store = FAISS.from_documents(documents, self.embeddings)
            
            load_time = time.time() - start_time
            self.logger.info(f"Loaded {len(documents)} documents in {load_time:.2f}s")
            
        except Exception as e:
            self.logger.error(f"Failed to load documents: {e}")
            raise
    
    def _get_cache_key(self, query: str, k: int) -> str:
        """Generate cache key for query"""
        return f"{hash(query)}_{k}"
    
    def _is_cache_valid(self, timestamp: float) -> bool:
        """Check if cache entry is still valid"""
        return time.time() - timestamp < self.cache_ttl
    
    async def search_async(self, query: str, k: int = 5) -> List[Dict]:
        """Async search with caching and monitoring"""
        self.monitor.increment_requests()
        
        try:
            # Check cache first
            cache_key = self._get_cache_key(query, k)
            if cache_key in self.cache:
                cached_result, timestamp = self.cache[cache_key]
                if self._is_cache_valid(timestamp):
                    self.monitor.record_cache_hit()
                    self.logger.info(f"Cache hit for query: {query[:50]}...")
                    return cached_result
                else:
                    # Remove expired cache entry
                    del self.cache[cache_key]
            
            self.monitor.record_cache_miss()
            
            # Perform search
            start_time = time.time()
            
            if self.vector_store is None:
                raise ValueError("Vector store not initialized. Call load_documents first.")
            
            # Generate embedding
            embedding_start = time.time()
            query_embedding = self.embeddings.embed_query(query)
            embedding_time = time.time() - embedding_start
            self.monitor.record_embedding_latency(embedding_time)
            
            # Search vector store
            search_start = time.time()
            results = self.vector_store.similarity_search_with_score(query, k=k)
            search_time = time.time() - search_start
            self.monitor.record_search_latency(search_time)
            
            # Format results
            formatted_results = []
            for doc, score in results:
                formatted_results.append({
                    'content': doc.page_content,
                    'metadata': doc.metadata,
                    'similarity_score': float(score),
                    'timestamp': datetime.now().isoformat()
                })
            
            # Cache results
            self.cache[cache_key] = (formatted_results, time.time())
            
            total_time = time.time() - start_time
            self.logger.info(
                f"Search completed - Query: {query[:50]}... | "
                f"Results: {len(formatted_results)} | "
                f"Time: {total_time:.3f}s"
            )
            
            return formatted_results
            
        except Exception as e:
            self.monitor.increment_errors()
            self.logger.error(f"Search failed for query '{query}': {e}")
            raise
    
    def search(self, query: str, k: int = 5) -> List[Dict]:
        """Synchronous search wrapper"""
        return asyncio.run(self.search_async(query, k))
    
    def get_health_status(self) -> Dict:
        """Get service health status"""
        stats = self.monitor.get_stats()
        
        # Determine health status
        health = "healthy"
        if stats.get('error_rate', 0) > 0.05:  # > 5% error rate
            health = "unhealthy"
        elif stats.get('avg_search_latency', 0) > 1.0:  # > 1s average latency
            health = "degraded"
        
        return {
            'status': health,
            'timestamp': datetime.now().isoformat(),
            'metrics': stats,
            'cache_size': len(self.cache),
            'vector_store_loaded': self.vector_store is not None
        }
    
    def clear_cache(self):
        """Clear the cache"""
        self.cache.clear()
        self.logger.info("Cache cleared")

print("✅ Production vector search service created!")