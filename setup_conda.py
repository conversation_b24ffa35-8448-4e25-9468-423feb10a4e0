#!/usr/bin/env python3
"""
Vector Database Learning Project - Conda Environment Setup
Automates conda environment creation and package installation
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def print_banner():
    """Print welcome banner"""
    banner = """
    🐍 Vector Database Learning Project - Conda Setup
    =================================================
    
    This script will create a conda environment and install
    all required packages for the vector database course.
    
    """
    print(banner)

def check_conda():
    """Check if conda is installed and available"""
    print("🔍 Checking conda installation...")
    
    try:
        result = subprocess.run(['conda', '--version'], 
                              capture_output=True, text=True, check=True)
        print(f"✅ {result.stdout.strip()}")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Conda not found!")
        print("💡 Please install Anaconda or Miniconda:")
        print("   • Anaconda: https://www.anaconda.com/download")
        print("   • Miniconda: https://docs.conda.io/en/latest/miniconda.html")
        return False

def create_conda_environment():
    """Create conda environment"""
    env_name = "vector-db-learning"
    python_version = "3.9"
    
    print(f"\n🏗️ Creating conda environment '{env_name}'...")
    
    # Check if environment already exists
    try:
        result = subprocess.run(['conda', 'env', 'list'], 
                              capture_output=True, text=True, check=True)
        if env_name in result.stdout:
            print(f"✅ Environment '{env_name}' already exists")
            
            # Ask user if they want to update it
            response = input(f"🔄 Do you want to update the existing environment? (y/n): ").lower()
            if response == 'y':
                print(f"🔄 Updating environment '{env_name}'...")
                return env_name
            else:
                print(f"📋 Using existing environment '{env_name}'")
                return env_name
    except subprocess.CalledProcessError:
        pass
    
    # Create new environment
    try:
        cmd = ['conda', 'create', '-n', env_name, f'python={python_version}', '-y']
        subprocess.run(cmd, check=True)
        print(f"✅ Environment '{env_name}' created successfully!")
        return env_name
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to create environment: {e}")
        return None

def install_packages_conda(env_name):
    """Install packages using conda and pip"""
    print(f"\n📦 Installing packages in environment '{env_name}'...")
    
    # Essential conda packages (faster installation)
    conda_packages = [
        'numpy=1.24.3',
        'pandas=2.0.3',
        'matplotlib=3.7.2',
        'seaborn=0.12.2',
        'scikit-learn=1.3.0',
        'scipy=1.11.1',
        'jupyter=1.0.0',
        'ipykernel=6.25.0',
        'requests=2.31.0',
        'tqdm=4.66.1',
        'python-dotenv=1.0.0'
    ]
    
    print("🔧 Installing core packages with conda...")
    try:
        cmd = ['conda', 'install', '-n', env_name, '-c', 'conda-forge'] + conda_packages + ['-y']
        subprocess.run(cmd, check=True)
        print("✅ Core packages installed with conda!")
    except subprocess.CalledProcessError as e:
        print(f"⚠️ Some conda packages failed: {e}")
        print("📝 Will try with pip instead...")
    
    # AI/ML packages that work better with pip
    pip_packages = [
        'langchain==0.1.20',
        'langchain-community==0.0.38',
        'langchain-core==0.1.52',
        'langchain-text-splitters==0.0.1',
        'langchain-huggingface==0.0.3',
        'langchain-google-genai==1.0.6',
        'langchain-pinecone==0.1.1',
        'langchain-chroma==0.1.1',
        'langchain-openai==0.1.8',
        'faiss-cpu==1.7.4',
        'pinecone-client==3.2.2',
        'chromadb==0.4.24',
        'sentence-transformers==2.2.2',
        'transformers==4.33.2',
        'torch==2.0.1',
        'huggingface-hub==0.16.4',
        'openai==1.35.5',
        'google-generativeai==0.5.4',
        'beautifulsoup4==4.12.2',
        'pypdf==3.15.2',
        'python-multipart==0.0.6',
        'plotly==5.15.0',
        'streamlit==1.25.0'
    ]
    
    print("🤖 Installing AI/ML packages with pip...")
    try:
        # Get conda environment python path
        if os.name == 'nt':  # Windows
            python_path = f"conda run -n {env_name} python"
            pip_cmd = f"conda run -n {env_name} pip"
        else:  # Unix/Linux/macOS
            python_path = f"conda run -n {env_name} python"
            pip_cmd = f"conda run -n {env_name} pip"
        
        # Install packages one by one to handle failures gracefully
        failed_packages = []
        for package in pip_packages:
            try:
                cmd = pip_cmd.split() + ['install', package]
                subprocess.run(cmd, check=True, capture_output=True)
                print(f"  ✅ {package}")
            except subprocess.CalledProcessError:
                failed_packages.append(package)
                print(f"  ❌ {package}")
        
        if failed_packages:
            print(f"\n⚠️ Failed to install {len(failed_packages)} packages:")
            for pkg in failed_packages:
                print(f"   • {pkg}")
            print("💡 You can install these manually later if needed")
        else:
            print("✅ All AI/ML packages installed successfully!")
            
    except Exception as e:
        print(f"❌ Failed to install pip packages: {e}")
        return False
    
    return True

def setup_jupyter_kernel(env_name):
    """Setup Jupyter kernel for the conda environment"""
    print(f"\n📓 Setting up Jupyter kernel for '{env_name}'...")
    
    try:
        cmd = ['conda', 'run', '-n', env_name, 'python', '-m', 'ipykernel', 'install', 
               '--user', '--name', env_name, '--display-name', f'Python (vector-db-learning)']
        subprocess.run(cmd, check=True, capture_output=True)
        print("✅ Jupyter kernel installed!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to setup Jupyter kernel: {e}")
        return False

def setup_env_file():
    """Set up environment file"""
    print("\n🔑 Setting up environment file...")
    
    env_template = Path(".env.template")
    env_file = Path(".env")
    
    if not env_template.exists():
        print("❌ .env.template file not found")
        return False
    
    if env_file.exists():
        print("✅ .env file already exists")
        return True
    
    try:
        shutil.copy(env_template, env_file)
        print("✅ .env file created from template")
        print("📝 Please edit .env file and add your API keys")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create .env file: {e}")
        return False

def print_next_steps(env_name):
    """Print next steps for the user"""
    next_steps = f"""
    🎉 Conda Environment Setup Complete!
    ====================================
    
    📋 Environment Details:
       • Name: {env_name}
       • Python: 3.9
       • Packages: Vector DB learning stack installed
    
    🚀 Next Steps:
    ==============
    
    1. Activate your conda environment:
       conda activate {env_name}
    
    2. Edit your .env file with API keys:
       • HF_TOKEN (HuggingFace - Free)
       • OPENAI_API_KEY (OpenAI - Paid)
       • GOOGLE_API_KEY (Google AI - Free tier)
       • PINECONE_API_KEY (Pinecone - Free tier)
    
    3. Start Jupyter Notebook:
       jupyter notebook
       
       OR use Jupyter Lab:
       jupyter lab
    
    4. Select the correct kernel:
       • Look for "Python (vector-db-learning)" in kernel list
       • If not visible, restart Jupyter
    
    5. Open the first notebook:
       01_Vector_Database_Fundamentals.ipynb
    
    📚 Learning Path:
    ================
    1. 01_Vector_Database_Fundamentals.ipynb (Start here!)
    2. 02_Embeddings_and_Models.ipynb
    3. 03_FAISS_Implementation.ipynb
    4. 04_Pinecone_Cloud_Database.ipynb
    5. 05_RAG_Implementation.ipynb
    6. 06_Production_Deployment.ipynb
    
    🔧 Useful Commands:
    ==================
    • Activate environment: conda activate {env_name}
    • Deactivate environment: conda deactivate
    • List environments: conda env list
    • Update packages: conda update --all
    • Remove environment: conda env remove -n {env_name}
    
    🆘 Troubleshooting:
    ==================
    • If packages are missing: conda activate {env_name} && pip install <package>
    • If kernel not visible: jupyter kernelspec list
    • If imports fail: restart Jupyter and check kernel selection
    
    Happy Learning with Conda! 🐍🚀
    """
    print(next_steps)

def main():
    """Main setup function"""
    print_banner()
    
    # Check prerequisites
    if not check_conda():
        sys.exit(1)
    
    # Create conda environment
    env_name = create_conda_environment()
    if not env_name:
        sys.exit(1)
    
    # Install packages
    if not install_packages_conda(env_name):
        print("\n⚠️ Some packages failed to install, but you can continue")
    
    # Setup Jupyter kernel
    setup_jupyter_kernel(env_name)
    
    # Setup environment file
    setup_env_file()
    
    # Print next steps
    print_next_steps(env_name)

if __name__ == "__main__":
    main()
