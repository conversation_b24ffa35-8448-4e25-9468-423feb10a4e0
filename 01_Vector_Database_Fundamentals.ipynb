# 📦 Step 2.1: Import Basic Libraries
# These are the fundamental tools we need

import numpy as np          # Working with arrays of numbers (our embeddings)
import pandas as pd         # Organizing data in tables
import matplotlib.pyplot as plt  # Creating charts and graphs

print("✅ Basic libraries imported!")
print("🎯 What we just did:")
print("   • numpy: Will help us work with embedding vectors")
print("   • pandas: Will organize our data nicely")
print("   • matplotlib: Will create visualizations")

# 📦 Step 2.2: Import Machine Learning Tools
# These help us calculate similarity between embeddings

from sklearn.metrics.pairwise import cosine_similarity, euclidean_distances
from sklearn.decomposition import PCA
import warnings
warnings.filterwarnings('ignore')  # Hide technical warnings

print("✅ Machine learning tools imported!")
print("🎯 What these do:")
print("   • cosine_similarity: Measures how similar two embeddings are")
print("   • euclidean_distances: Another way to measure similarity")
print("   • PCA: Helps us visualize high-dimensional data in 2D")

# 📦 Step 2.3: Test Our Setup
# Let's make sure everything works by creating a simple example

# Create two simple "embeddings" (just lists of numbers)
embedding1 = np.array([1, 2, 3])  # Represents some text
embedding2 = np.array([1, 2, 4])  # Represents similar text
embedding3 = np.array([5, 6, 7])  # Represents different text

print("🧪 Testing our setup:")
print(f"Embedding 1: {embedding1}")
print(f"Embedding 2: {embedding2}")
print(f"Embedding 3: {embedding3}")
print("\n✅ Great! We can create and display embeddings.")
print("🎯 Next: We'll learn how to measure similarity between them.")

# 🔐 Load Environment Variables
from dotenv import load_dotenv
import os

# Load environment variables from .env file
load_dotenv()

# Set up API keys
os.environ['HF_TOKEN'] = os.getenv("HF_TOKEN", "")
os.environ['OPENAI_API_KEY'] = os.getenv("OPENAI_API_KEY", "")
os.environ['GOOGLE_API_KEY'] = os.getenv("GOOGLE_API_KEY", "")
os.environ['PINECONE_API_KEY'] = os.getenv("PINECONE_API_KEY", "")

print("🔑 Environment variables loaded!")
print(f"📝 HF_TOKEN: {'✅ Set' if os.getenv('HF_TOKEN') else '❌ Not set'}")
print(f"📝 OPENAI_API_KEY: {'✅ Set' if os.getenv('OPENAI_API_KEY') else '❌ Not set'}")
print(f"📝 GOOGLE_API_KEY: {'✅ Set' if os.getenv('GOOGLE_API_KEY') else '❌ Not set'}")
print(f"📝 PINECONE_API_KEY: {'✅ Set' if os.getenv('PINECONE_API_KEY') else '❌ Not set'}")

# 🧮 Basic Vector Operations Demo
# Let's create some sample vectors and explore similarity metrics

# Create sample vectors representing different concepts
vectors = {
    'cat': np.array([0.8, 0.6, 0.1, 0.2]),
    'kitten': np.array([0.7, 0.7, 0.2, 0.1]),
    'dog': np.array([0.6, 0.4, 0.8, 0.3]),
    'car': np.array([0.1, 0.2, 0.1, 0.9]),
    'vehicle': np.array([0.2, 0.1, 0.2, 0.8])
}

print("🐱 Sample Vectors (representing concepts):")
for name, vector in vectors.items():
    print(f"{name:8}: {vector}")

print("\n📊 Let's calculate similarities!")

# 🔍 Calculate Cosine Similarities
def calculate_cosine_similarity(vec1, vec2):
    """Calculate cosine similarity between two vectors"""
    return cosine_similarity([vec1], [vec2])[0][0]

def calculate_euclidean_distance(vec1, vec2):
    """Calculate Euclidean distance between two vectors"""
    return euclidean_distances([vec1], [vec2])[0][0]

# Compare 'cat' with all other vectors
query_vector = vectors['cat']
print(f"🎯 Comparing 'cat' vector with others:\n")
print(f"{'Concept':<10} {'Cosine Sim':<12} {'Euclidean Dist':<15} {'Interpretation'}")
print("-" * 60)

for name, vector in vectors.items():
    if name != 'cat':
        cos_sim = calculate_cosine_similarity(query_vector, vector)
        euc_dist = calculate_euclidean_distance(query_vector, vector)
        
        # Interpretation based on cosine similarity
        if cos_sim > 0.8:
            interpretation = "Very Similar 🟢"
        elif cos_sim > 0.5:
            interpretation = "Somewhat Similar 🟡"
        else:
            interpretation = "Different 🔴"
            
        print(f"{name:<10} {cos_sim:<12.3f} {euc_dist:<15.3f} {interpretation}")

# 📊 Visualize Vector Similarities
# Create a similarity matrix and visualize it

vector_names = list(vectors.keys())
n_vectors = len(vector_names)

# Create similarity matrix
similarity_matrix = np.zeros((n_vectors, n_vectors))

for i, name1 in enumerate(vector_names):
    for j, name2 in enumerate(vector_names):
        similarity_matrix[i][j] = calculate_cosine_similarity(vectors[name1], vectors[name2])

# Plot heatmap
plt.figure(figsize=(10, 8))
sns.heatmap(similarity_matrix, 
            xticklabels=vector_names, 
            yticklabels=vector_names,
            annot=True, 
            cmap='RdYlBu_r',
            center=0.5,
            square=True,
            fmt='.3f')
plt.title('🔥 Cosine Similarity Matrix\n(Darker = More Similar)', fontsize=14, fontweight='bold')
plt.xlabel('Concepts', fontweight='bold')
plt.ylabel('Concepts', fontweight='bold')
plt.tight_layout()
plt.show()

print("\n💡 Insights:")
print("• 'cat' and 'kitten' are most similar (both felines)")
print("• 'car' and 'vehicle' are highly similar (both transportation)")
print("• Animals vs vehicles show low similarity")

# 📊 Visualize Vector Similarities
# Create a similarity matrix and visualize it

vector_names = list(vectors.keys())
n_vectors = len(vector_names)

# Create similarity matrix
similarity_matrix = np.zeros((n_vectors, n_vectors))

for i, name1 in enumerate(vector_names):
    for j, name2 in enumerate(vector_names):
        similarity_matrix[i][j] = calculate_cosine_similarity(vectors[name1], vectors[name2])

# Plot heatmap
plt.figure(figsize=(10, 8))
sns.heatmap(similarity_matrix, 
            xticklabels=vector_names, 
            yticklabels=vector_names,
            annot=True, 
            cmap='RdYlBu_r',
            center=0.5,
            square=True,
            fmt='.3f')
plt.title('🔥 Cosine Similarity Matrix\n(Darker = More Similar)', fontsize=14, fontweight='bold')
plt.xlabel('Concepts', fontweight='bold')
plt.ylabel('Concepts', fontweight='bold')
plt.tight_layout()
plt.show()

print("\n💡 Insights:")
print("• 'cat' and 'kitten' are most similar (both felines)")
print("• 'car' and 'vehicle' are highly similar (both transportation)")
print("• Animals vs vehicles show low similarity")

# 🚀 Load Pre-trained Embedding Model
from langchain_huggingface import HuggingFaceEmbeddings

print("📥 Loading HuggingFace embedding model...")
print("⏳ This might take a moment for first-time download...")

# Initialize embeddings model
embeddings = HuggingFaceEmbeddings(
    model_name="all-MiniLM-L6-v2",  # Fast and efficient model
    model_kwargs={'device': 'cpu'},  # Use CPU for compatibility
    encode_kwargs={'normalize_embeddings': True}  # Normalize for cosine similarity
)

print("✅ Embedding model loaded successfully!")
print(f"📏 Model creates {len(embeddings.embed_query('test'))}-dimensional vectors")

# 📝 Create Sample Documents for Testing
sample_documents = [
    "The cat sat on the mat and purred contentedly.",
    "A small kitten played with a ball of yarn.",
    "The dog barked loudly at the mailman.",
    "Python is a popular programming language.",
    "Machine learning models can process text data.",
    "The red car drove down the highway quickly.",
    "Artificial intelligence is transforming technology.",
    "The weather today is sunny and warm.",
    "Cats and dogs are popular household pets.",
    "Programming requires logical thinking and problem-solving."
]

print("📚 Sample Documents:")
for i, doc in enumerate(sample_documents, 1):
    print(f"{i:2d}. {doc}")

print(f"\n📊 Total documents: {len(sample_documents)}")

# 🔄 Generate Embeddings for All Documents
print("🔄 Generating embeddings for all documents...")
print("⏳ This may take a few seconds...")

# Generate embeddings
document_embeddings = embeddings.embed_documents(sample_documents)

print(f"✅ Generated embeddings for {len(document_embeddings)} documents")
print(f"📏 Each embedding has {len(document_embeddings[0])} dimensions")

# Convert to numpy array for easier manipulation
embeddings_array = np.array(document_embeddings)
print(f"📊 Embeddings array shape: {embeddings_array.shape}")

# 🔍 Semantic Search Function
def semantic_search(query, documents, embeddings_array, top_k=3):
    """
    Perform semantic search to find most similar documents
    
    Args:
        query (str): Search query
        documents (list): List of documents
        embeddings_array (np.array): Document embeddings
        top_k (int): Number of top results to return
    
    Returns:
        list: Top-k most similar documents with scores
    """
    # Generate embedding for query
    query_embedding = embeddings.embed_query(query)
    
    # Calculate similarities
    similarities = cosine_similarity([query_embedding], embeddings_array)[0]
    
    # Get top-k indices
    top_indices = np.argsort(similarities)[::-1][:top_k]
    
    # Prepare results
    results = []
    for idx in top_indices:
        results.append({
            'document': documents[idx],
            'similarity': similarities[idx],
            'rank': len(results) + 1
        })
    
    return results

print("✅ Semantic search function ready!")

# 🎯 Test Semantic Search
test_queries = [
    "pets and animals",
    "coding and software development",
    "transportation and vehicles",
    "artificial intelligence"
]

for query in test_queries:
    print(f"\n🔍 Query: '{query}'")
    print("=" * 50)
    
    results = semantic_search(query, sample_documents, embeddings_array, top_k=3)
    
    for result in results:
        print(f"\n{result['rank']}. Similarity: {result['similarity']:.3f}")
        print(f"   📄 {result['document']}")
        
        # Add interpretation
        if result['similarity'] > 0.7:
            print("   🟢 Highly relevant")
        elif result['similarity'] > 0.5:
            print("   🟡 Moderately relevant")
        else:
            print("   🔴 Low relevance")

# 📊 Visualize Document Embeddings with PCA
# Reduce dimensionality for visualization

print("📊 Creating 2D visualization of document embeddings...")

# Apply PCA to reduce to 2D
pca = PCA(n_components=2)
embeddings_2d = pca.fit_transform(embeddings_array)

# Create the plot
plt.figure(figsize=(12, 8))

# Plot each document
for i, (x, y) in enumerate(embeddings_2d):
    plt.scatter(x, y, s=100, alpha=0.7)
    
    # Add document number as label
    plt.annotate(f'Doc {i+1}', (x, y), 
                xytext=(5, 5), textcoords='offset points',
                fontsize=10, fontweight='bold')

plt.title('📊 Document Embeddings Visualization (PCA 2D)\n' + 
          'Similar documents cluster together', 
          fontsize=14, fontweight='bold')
plt.xlabel(f'First Principal Component ({pca.explained_variance_ratio_[0]:.1%} variance)', 
           fontweight='bold')
plt.ylabel(f'Second Principal Component ({pca.explained_variance_ratio_[1]:.1%} variance)', 
           fontweight='bold')
plt.grid(True, alpha=0.3)
plt.tight_layout()
plt.show()

print(f"\n💡 PCA explains {sum(pca.explained_variance_ratio_):.1%} of the variance in 2D")
print("📍 Documents with similar content should appear closer together")