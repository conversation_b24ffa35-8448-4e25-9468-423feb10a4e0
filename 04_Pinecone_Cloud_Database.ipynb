# 🛠️ Setup and Imports
import numpy as np
import pandas as pd
import time
import uuid
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Load environment variables
from dotenv import load_dotenv
import os
load_dotenv()

# Set up API keys
os.environ['HF_TOKEN'] = os.getenv("HF_TOKEN", "")
os.environ['PINECONE_API_KEY'] = os.getenv("PINECONE_API_KEY", "")

# Check if Pinecone API key is available
pinecone_available = bool(os.getenv('PINECONE_API_KEY'))

print("✅ Basic setup complete!")
print(f"🌲 Pinecone API Key: {'✅ Available' if pinecone_available else '❌ Not found'}")

if not pinecone_available:
    print("\n⚠️ Pinecone API key not found!")
    print("💡 To use Pinecone:")
    print("   1. Sign up at https://www.pinecone.io/")
    print("   2. Get your API key from the dashboard")
    print("   3. Add PINECONE_API_KEY to your .env file")
    print("   4. Restart this notebook")
else:
    print("🚀 Ready to work with Pinecone!")

# 🌲 Import Pinecone (if available)
if pinecone_available:
    try:
        from pinecone import Pinecone, ServerlessSpec
        from langchain_pinecone import PineconeVectorStore
        from langchain_huggingface import HuggingFaceEmbeddings
        from langchain_core.documents import Document
        
        print("✅ Pinecone imports successful!")
        
        # Initialize Pinecone
        pc = Pinecone(api_key=os.getenv('PINECONE_API_KEY'))
        print("🔗 Connected to Pinecone!")
        
    except Exception as e:
        print(f"❌ Failed to import/connect to Pinecone: {e}")
        print("💡 Make sure you have installed: pip install pinecone-client")
        pinecone_available = False
else:
    print("⏭️ Skipping Pinecone imports (API key not available)")

# 🚀 Load Embedding Model
if pinecone_available:
    print("📥 Loading embedding model...")
    
    embeddings = HuggingFaceEmbeddings(
        model_name="all-MiniLM-L6-v2",
        model_kwargs={'device': 'cpu'},
        encode_kwargs={'normalize_embeddings': True}
    )
    
    # Test embedding to get dimension
    test_embedding = embeddings.embed_query("test")
    embedding_dim = len(test_embedding)
    
    print(f"✅ Embedding model loaded!")
    print(f"📏 Embedding dimension: {embedding_dim}")
else:
    print("⏭️ Skipping embedding model (Pinecone not available)")

# 🏗️ Create Pinecone Index
if pinecone_available:
    # Index configuration
    index_name = "vector-learning-demo"
    
    print(f"🏗️ Setting up Pinecone index: {index_name}")
    
    # Check if index already exists
    existing_indexes = [index.name for index in pc.list_indexes()]
    
    if index_name in existing_indexes:
        print(f"📋 Index '{index_name}' already exists")
        print("🔄 Using existing index...")
    else:
        print(f"🆕 Creating new index '{index_name}'...")
        
        # Create index with serverless configuration
        pc.create_index(
            name=index_name,
            dimension=embedding_dim,
            metric="cosine",  # cosine, euclidean, or dotproduct
            spec=ServerlessSpec(
                cloud="aws",
                region="us-east-1"  # Choose region closest to you
            )
        )
        
        print("⏳ Waiting for index to be ready...")
        # Wait for index to be ready
        while not pc.describe_index(index_name).status['ready']:
            time.sleep(1)
        
        print("✅ Index created and ready!")
    
    # Connect to the index
    index = pc.Index(index_name)
    
    # Get index stats
    stats = index.describe_index_stats()
    print(f"\n📊 Index Statistics:")
    print(f"   📏 Dimension: {stats.dimension}")
    print(f"   🔢 Total vectors: {stats.total_vector_count}")
    print(f"   📦 Namespaces: {len(stats.namespaces)}")
    
else:
    print("⏭️ Skipping index creation (Pinecone not available)")

# 📚 Prepare Sample Documents with Rich Metadata
if pinecone_available:
    sample_documents = [
        {
            "text": "The cat sat on the mat and purred contentedly.",
            "category": "animals",
            "topic": "pets",
            "sentiment": "positive",
            "length": "short",
            "source": "story"
        },
        {
            "text": "Python is a versatile programming language for data science.",
            "category": "technology",
            "topic": "programming",
            "sentiment": "neutral",
            "length": "short",
            "source": "documentation"
        },
        {
            "text": "Machine learning algorithms can process large datasets efficiently.",
            "category": "technology",
            "topic": "ai",
            "sentiment": "positive",
            "length": "medium",
            "source": "article"
        },
        {
            "text": "The weather forecast predicts heavy rain for tomorrow.",
            "category": "weather",
            "topic": "forecast",
            "sentiment": "neutral",
            "length": "short",
            "source": "news"
        },
        {
            "text": "Vector databases enable semantic search capabilities for AI applications.",
            "category": "technology",
            "topic": "databases",
            "sentiment": "positive",
            "length": "medium",
            "source": "tutorial"
        }
    ]
    
    print(f"📚 Prepared {len(sample_documents)} documents with metadata")
    print("\n📄 Sample document structure:")
    for key, value in sample_documents[0].items():
        print(f"   {key}: {value}")
        
else:
    print("⏭️ Skipping document preparation (Pinecone not available)")