# ===============================================================================
# 🕸️ LANGGRAPH MASTERY - DEPENDENCIES
# ===============================================================================
# Complete requirements for LangGraph learning: from basics to advanced
# AI workflow systems with multi-agent coordination and state management
# ===============================================================================

# Core Python packages
numpy==1.24.3
pandas==2.0.3
matplotlib==3.7.2
seaborn==0.12.2
python-dotenv==1.0.0
pyyaml==6.0.1
requests==2.31.0

# ===============================================================================
# 🕸️ LANGGRAPH ECOSYSTEM
# ===============================================================================

# LangGraph - The star of the show!
langgraph>=0.0.40                    # Graph-based AI workflows
langchain>=0.1.20                    # LangChain foundation
langchain-core>=0.1.52               # Core LangChain components
langchain-community>=0.0.38          # Community integrations

# State management and persistence
langchain-experimental>=0.0.58       # Experimental features
langsmith>=0.1.0                     # LangSmith for monitoring

# ===============================================================================
# 🤖 AI MODEL INTEGRATIONS
# ===============================================================================

# OpenAI Integration
langchain-openai>=0.1.8              # OpenAI models (GPT-3.5, GPT-4)
openai>=1.35.5                       # Direct OpenAI API

# Google AI Integration  
langchain-google-genai>=1.0.6        # Google Gemini models
google-generativeai>=0.5.4           # Direct Google AI API

# Anthropic Integration
langchain-anthropic>=0.1.15          # Claude models
anthropic>=0.25.0                    # Direct Anthropic API

# HuggingFace Integration
langchain-huggingface>=0.0.3         # HuggingFace models
transformers>=4.33.2                 # Transformer models
sentence-transformers>=2.6.0         # Embedding models
torch>=2.0.1                         # PyTorch backend

# ===============================================================================
# 🔧 WORKFLOW TOOLS AND UTILITIES
# ===============================================================================

# Web scraping and search
beautifulsoup4==4.12.2               # HTML parsing
requests-html==0.10.0                # JavaScript-enabled scraping
selenium==4.15.0                     # Browser automation
duckduckgo-search==3.9.6             # Search engine integration

# Document processing
pypdf==3.15.2                        # PDF processing
python-docx==0.8.11                  # Word document handling
openpyxl==3.1.2                      # Excel file handling

# Data validation and parsing
pydantic>=2.0.0                      # Data validation
jsonschema==4.19.1                   # JSON schema validation

# ===============================================================================
# 📊 VISUALIZATION AND MONITORING
# ===============================================================================

# Graph visualization
graphviz==0.20.1                     # Graph rendering
networkx==3.1                        # Graph analysis
plotly==5.17.0                       # Interactive plots
ipywidgets==8.1.0                    # Jupyter widgets

# Monitoring and logging
structlog==23.1.0                    # Structured logging
rich==13.5.2                         # Rich terminal output
tqdm==4.66.1                         # Progress bars

# ===============================================================================
# 🗄️ DATABASE AND STORAGE
# ===============================================================================

# Vector databases (choose based on your needs)
faiss-cpu==1.7.4                     # Local vector search
chromadb>=0.4.15                     # Local vector database
pinecone-client>=2.2.4               # Pinecone cloud vector DB

# Traditional databases
sqlite3                              # Built-in SQLite
sqlalchemy==2.0.21                   # SQL toolkit
redis==5.0.0                         # Redis for caching

# ===============================================================================
# 🌐 WEB FRAMEWORKS AND APIs
# ===============================================================================

# Web frameworks for deployment
fastapi==0.103.1                     # Modern API framework
uvicorn==0.23.2                      # ASGI server
streamlit>=1.28.0                    # Quick web apps
gradio>=3.50.0                       # ML model interfaces

# HTTP utilities
httpx==0.25.0                        # Async HTTP client
aiohttp==3.8.6                       # Async HTTP framework

# ===============================================================================
# 🧪 TESTING AND DEVELOPMENT
# ===============================================================================

# Testing frameworks
pytest==7.4.2                        # Testing framework
pytest-asyncio==0.21.1               # Async testing
pytest-mock==3.11.1                  # Mocking utilities

# Development tools
black==23.9.1                        # Code formatting
flake8==6.1.0                        # Code linting
mypy==1.5.1                          # Type checking

# Jupyter and notebooks
jupyter==1.0.0                       # Jupyter ecosystem
ipykernel==6.25.0                    # Jupyter kernel
notebook==7.0.2                      # Jupyter notebook

# ===============================================================================
# ⚡ PERFORMANCE AND ASYNC
# ===============================================================================

# Async utilities
asyncio                              # Built-in async support
aiofiles==23.2.1                     # Async file operations
asyncpg==0.28.0                      # Async PostgreSQL

# Performance monitoring
psutil==5.9.5                        # System monitoring
memory-profiler==0.61.0              # Memory profiling

# ===============================================================================
# 🔐 SECURITY AND AUTHENTICATION
# ===============================================================================

# Security utilities
cryptography==41.0.4                 # Cryptographic functions
python-jose==3.3.0                   # JWT handling
passlib==1.7.4                       # Password hashing

# ===============================================================================
# 📦 DEPLOYMENT AND CONTAINERIZATION
# ===============================================================================

# Container and deployment
docker>=6.1.0                        # Docker Python SDK
kubernetes>=27.2.0                   # Kubernetes Python client

# Cloud providers
boto3>=1.28.0                        # AWS SDK
google-cloud-storage>=2.10.0         # Google Cloud Storage
azure-storage-blob>=12.17.0          # Azure Blob Storage

# ===============================================================================
# 🎯 SPECIALIZED LANGGRAPH TOOLS
# ===============================================================================

# Graph algorithms and analysis
igraph>=0.10.6                       # Graph analysis library
python-louvain>=0.16                 # Community detection

# Workflow orchestration
prefect>=2.13.0                      # Workflow orchestration (alternative)
celery>=5.3.0                        # Distributed task queue

# State machines
transitions>=0.9.0                   # State machine library
statemachine>=2.1.0                  # Another state machine option

# ===============================================================================
# 📋 INSTALLATION NOTES
# ===============================================================================
#
# 🚀 QUICK INSTALL:
#   pip install -r requirements.txt
#
# 🐍 CONDA ENVIRONMENT:
#   conda create -n langgraph-learning python=3.9
#   conda activate langgraph-learning
#   pip install -r requirements.txt
#
# 🔧 SYSTEM DEPENDENCIES:
#   • Graphviz: brew install graphviz (macOS) or apt-get install graphviz (Ubuntu)
#   • Chrome/Chromium: For Selenium web automation
#
# 💰 COST CONSIDERATIONS:
#   • OpenAI API: ~$0.002 per 1K tokens (GPT-3.5-turbo)
#   • Google AI: Free tier available
#   • Anthropic: ~$0.008 per 1K tokens (Claude)
#   • Vector databases: Most have free tiers
#
# ===============================================================================
# 🎯 OPTIONAL DEPENDENCIES
# ===============================================================================
#
# Install these based on your specific needs:
#
# 🔊 Audio processing:
#   whisper-openai>=20230918
#   pydub>=0.25.1
#
# 🖼️ Image processing:
#   pillow>=10.0.0
#   opencv-python>=4.8.0
#
# 📊 Advanced data science:
#   scikit-learn>=1.3.0
#   scipy>=1.11.1
#   statsmodels>=0.14.0
#
# 🌐 Advanced web scraping:
#   scrapy>=2.11.0
#   playwright>=1.39.0
#
# ===============================================================================
