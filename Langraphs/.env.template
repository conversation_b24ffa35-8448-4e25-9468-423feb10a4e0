# ===============================================================================
# 🕸️ LANGGRAPH MASTERY - ENVIRONMENT CONFIGURATION
# ===============================================================================
# Configuration for LangGraph learning: AI workflows, multi-agent systems,
# and advanced state management
#
# INSTRUCTIONS:
# 1. Copy this file to .env (remove .template from filename)
# 2. Fill in your API keys and configuration values
# 3. Never commit the .env file to version control
# ===============================================================================

# ===============================================================================
# 🤖 AI MODEL API KEYS
# ===============================================================================

# 🌟 OpenAI API Key (PAID - Premium quality)
# Get from: https://platform.openai.com/api-keys
# Purpose: High-quality LLM for complex reasoning and multi-agent systems
# Cost: ~$0.002 per 1K tokens for GPT-3.5-turbo, ~$0.03 for GPT-4
# Models: gpt-3.5-turbo, gpt-4, gpt-4-turbo
OPENAI_API_KEY=your_openai_api_key_here

# 🎯 Google AI API Key (FREE tier available)
# Get from: https://makersuite.google.com/app/apikey
# Purpose: Free alternative with good performance for learning
# Cost: FREE tier with generous limits, then pay-per-use
# Models: gemini-pro, gemini-pro-vision
GOOGLE_API_KEY=your_google_api_key_here

# 🧠 Anthropic API Key (PAID - High quality)
# Get from: https://console.anthropic.com/
# Purpose: Claude models for advanced reasoning
# Cost: ~$0.008 per 1K tokens for Claude-3-Haiku
# Models: claude-3-haiku, claude-3-sonnet, claude-3-opus
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# 🤗 HuggingFace Token (FREE - for local models)
# Get from: https://huggingface.co/settings/tokens
# Purpose: Access to open-source models and embeddings
# Cost: FREE for most models
HUGGINGFACE_API_TOKEN=your_huggingface_token_here

# ===============================================================================
# 🔍 SEARCH AND WEB TOOLS
# ===============================================================================

# 🔍 Search Engine APIs
# Tavily Search API (Recommended for LangGraph)
TAVILY_API_KEY=your_tavily_api_key_here

# SerpAPI (Google Search results)
SERPAPI_API_KEY=your_serpapi_key_here

# Bing Search API
BING_SEARCH_API_KEY=your_bing_search_key_here

# ===============================================================================
# 🗄️ VECTOR DATABASE CONFIGURATIONS
# ===============================================================================

# 📊 Pinecone (Cloud vector database)
PINECONE_API_KEY=your_pinecone_api_key_here
PINECONE_ENVIRONMENT=your_pinecone_environment_here

# 🍃 MongoDB Atlas (Vector search)
MONGODB_URI=your_mongodb_connection_string_here

# ⭐ AstraDB (Cassandra-based)
ASTRA_DB_API_ENDPOINT=your_astra_db_endpoint_here
ASTRA_DB_APPLICATION_TOKEN=your_astra_db_token_here

# ===============================================================================
# 📊 MONITORING AND OBSERVABILITY
# ===============================================================================

# 🔍 LangSmith (LangChain monitoring)
LANGCHAIN_TRACING_V2=true
LANGCHAIN_API_KEY=your_langsmith_api_key_here
LANGCHAIN_PROJECT=langgraph-learning

# 📈 Weights & Biases (Experiment tracking)
WANDB_API_KEY=your_wandb_api_key_here
WANDB_PROJECT=langgraph-experiments

# ===============================================================================
# 🌐 WEB SCRAPING AND AUTOMATION
# ===============================================================================

# 🌐 Browser automation
SELENIUM_DRIVER_PATH=/path/to/chromedriver
PLAYWRIGHT_BROWSERS_PATH=/path/to/playwright/browsers

# 🔐 Proxy settings (if needed)
HTTP_PROXY=http://your-proxy:port
HTTPS_PROXY=https://your-proxy:port

# ===============================================================================
# 🗄️ DATABASE CONNECTIONS
# ===============================================================================

# 🐘 PostgreSQL
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=langgraph_db
POSTGRES_USER=your_postgres_user
POSTGRES_PASSWORD=your_postgres_password

# 🔴 Redis (for caching and state persistence)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
REDIS_DB=0

# ===============================================================================
# 🔧 LANGGRAPH CONFIGURATION
# ===============================================================================

# 🕸️ Graph execution settings
LANGGRAPH_MAX_ITERATIONS=50
LANGGRAPH_TIMEOUT_SECONDS=300
LANGGRAPH_ENABLE_CHECKPOINTS=true

# 🤖 Multi-agent settings
MAX_CONCURRENT_AGENTS=5
AGENT_TIMEOUT_SECONDS=60
ENABLE_AGENT_MEMORY=true

# 🔄 Retry and error handling
MAX_RETRIES=3
RETRY_DELAY_SECONDS=2
ENABLE_FALLBACK_AGENTS=true

# ===============================================================================
# 📊 PERFORMANCE AND SCALING
# ===============================================================================

# ⚡ Performance settings
ENABLE_ASYNC_EXECUTION=true
MAX_CONCURRENT_TASKS=10
BATCH_SIZE=5

# 💾 Memory management
MAX_MEMORY_MB=4096
ENABLE_MEMORY_MONITORING=true
MEMORY_WARNING_THRESHOLD=80

# 🔄 Caching
ENABLE_RESPONSE_CACHING=true
CACHE_TTL_SECONDS=3600
CACHE_MAX_SIZE=1000

# ===============================================================================
# 🔐 SECURITY SETTINGS
# ===============================================================================

# 🔒 API security
API_RATE_LIMIT_PER_MINUTE=60
ENABLE_API_KEY_ROTATION=false
API_KEY_ROTATION_DAYS=30

# 🛡️ Content filtering
ENABLE_CONTENT_FILTERING=true
MAX_INPUT_LENGTH=10000
BLOCKED_DOMAINS=malicious-site.com,spam-site.com

# ===============================================================================
# 📝 LOGGING AND DEBUGGING
# ===============================================================================

# 📋 Logging configuration
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE_PATH=./logs/langgraph.log
ENABLE_FILE_LOGGING=true

# 🐛 Debug settings
DEBUG_MODE=false
ENABLE_GRAPH_VISUALIZATION=true
SAVE_INTERMEDIATE_STATES=false

# ===============================================================================
# 🚀 DEPLOYMENT SETTINGS
# ===============================================================================

# 🌐 Web server configuration
HOST=0.0.0.0
PORT=8000
WORKERS=4

# 🐳 Container settings
CONTAINER_NAME=langgraph-app
CONTAINER_PORT=8000

# ☁️ Cloud deployment
CLOUD_PROVIDER=aws
REGION=us-west-2
ENVIRONMENT=development

# ===============================================================================
# 📋 CONFIGURATION GUIDE
# ===============================================================================
#
# 🎯 QUICK START CONFIGURATIONS:
#
# 🆓 FREE SETUP (No costs):
#   1. Use GOOGLE_API_KEY for LLM (free tier)
#   2. Use HUGGINGFACE_API_TOKEN for embeddings
#   3. Use local vector storage (ChromaDB/FAISS)
#   4. Use local Redis for state persistence
#
# 💰 PREMIUM SETUP (Best performance):
#   1. Use OPENAI_API_KEY for LLM (paid)
#   2. Use PINECONE_API_KEY for vector storage
#   3. Use LANGCHAIN_API_KEY for monitoring
#   4. Use TAVILY_API_KEY for web search
#
# 🏢 PRODUCTION SETUP:
#   1. Use managed databases (PostgreSQL, Redis)
#   2. Enable monitoring and logging
#   3. Configure proper security settings
#   4. Set up load balancing and scaling
#
# ===============================================================================
# 🔧 TROUBLESHOOTING
# ===============================================================================
#
# ❌ Common Issues:
#
# 1. "API key not found" errors:
#    • Check if .env file is in the correct directory
#    • Verify API key format and validity
#    • Ensure no extra spaces in keys
#
# 2. "Rate limit exceeded" errors:
#    • Reduce MAX_CONCURRENT_AGENTS
#    • Increase RETRY_DELAY_SECONDS
#    • Check API usage limits
#
# 3. "Graph execution timeout" errors:
#    • Increase LANGGRAPH_TIMEOUT_SECONDS
#    • Reduce LANGGRAPH_MAX_ITERATIONS
#    • Check for infinite loops in graph logic
#
# 4. "Memory errors" with large graphs:
#    • Increase MAX_MEMORY_MB
#    • Enable ENABLE_MEMORY_MONITORING
#    • Reduce BATCH_SIZE
#
# ===============================================================================
# 💡 OPTIMIZATION TIPS
# ===============================================================================
#
# 🚀 Performance:
#   • Enable async execution for better throughput
#   • Use caching to reduce API calls
#   • Batch similar operations together
#   • Monitor memory usage with large graphs
#
# 💰 Cost:
#   • Use Google AI free tier for learning
#   • Cache responses to reduce API calls
#   • Use local models when possible
#   • Monitor API usage regularly
#
# 🎯 Reliability:
#   • Enable checkpoints for long-running workflows
#   • Implement proper retry logic
#   • Use fallback agents for critical paths
#   • Monitor graph execution health
#
# ===============================================================================
