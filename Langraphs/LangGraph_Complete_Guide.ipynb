{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🕸️ LangGraph Complete Guide - Every Concept Explained Simply\n", "\n", "## 🤔 What Problem Does LangGraph Solve?\n", "\n", "### 🔴 **Traditional AI Problems:**\n", "```\n", "User: \"Research and write a report about AI\"\n", "Traditional AI: \"Here's what I know about AI\" (limited, static response)\n", "```\n", "\n", "### 🟢 **LangGraph Solution:**\n", "```\n", "User: \"Research and write a report about AI\"\n", "LangGraph: Search Web → Analyze Results → Need More Info? → Search Again → Write Report\n", "```\n", "\n", "## 🎯 Real-World Examples\n", "\n", "### 📋 **Research Assistant Workflow:**\n", "1. **Receive Question** → \"What are the latest AI trends?\"\n", "2. **Search Web** → Find recent articles\n", "3. **Analyze Quality** → Are sources reliable?\n", "4. **Decision Point** → Need more sources? YES → Go back to step 2\n", "5. **Synthesize** → Combine information\n", "6. **Generate Report** → Create final answer\n", "\n", "### 🎯 **Customer Support Workflow:**\n", "1. **Receive Ticket** → Customer complaint\n", "2. **Classify Issue** → Technical, billing, or general?\n", "3. **Route Decision** → Simple issue? → Auto-resolve : Escalate to human\n", "4. **Follow Up** → Check if resolved\n", "\n", "## 🧩 Core LangGraph Concepts (Building Blocks)\n", "\n", "### 1. **Graph** 🕸️\n", "- **What**: The overall workflow container\n", "- **Why**: Organizes how different AI operations connect\n", "- **Problem Solved**: No more rigid, linear AI responses\n", "- **Real-World Analogy**: Like a flowchart that AI can follow\n", "\n", "### 2. **Nodes** 🔵\n", "- **What**: Individual functions that do specific tasks\n", "- **Why**: Break complex problems into manageable pieces\n", "- **Problem Solved**: Each node has one clear responsibility\n", "- **Real-World Analogy**: Workers in an assembly line\n", "\n", "### 3. **Edges** ➡️\n", "- **What**: Connections that define data flow between nodes\n", "- **Why**: Control the order and direction of operations\n", "- **Problem Solved**: Ensures logical sequence of operations\n", "- **Real-World Analogy**: Conveyor belts between workstations\n", "\n", "### 4. **State** 📊\n", "- **What**: Information that flows between nodes\n", "- **Why**: Nodes need to share data and context\n", "- **Problem Solved**: Maintains context across multiple steps\n", "- **Real-World Analogy**: Shared workspace where workers pass information\n", "\n", "### 5. **Conditional Logic** 🤔\n", "- **What**: Decision points that choose different paths\n", "- **Why**: AI needs to make smart decisions based on results\n", "- **Problem Solved**: Dynamic workflows that adapt to situations\n", "- **Real-World Analogy**: Traffic lights that change based on conditions\n", "\n", "Let's build these concepts step by step! 🚀"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🛠️ Step 1: Environment Setup and Basic Imports\n", "\n", "### 🎯 What We're Importing and Why:\n", "- **Graph**: The main container for our workflow\n", "- **Image/display**: Tools to visualize our graph structure\n", "- **AI Models**: For real AI-powered nodes\n", "\n", "### 💡 Think of This Like:\n", "Setting up a workshop with all the tools we need before building something"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🛠️ Step 1.1: Import Core LangGraph Components\n", "from langgraph.graph import Graph\n", "from IPython.display import Image, display\n", "\n", "print(\"✅ LangGraph imported successfully!\")\n", "print(\"\\n🎯 What we just imported:\")\n", "print(\"   • Graph: The main container for our AI workflow\")\n", "print(\"   • Image/display: Tools to see our workflow visually\")\n", "print(\"\\n💡 Think of Graph like a smart flowchart that AI can follow!\")\n", "print(\"🔧 Ready to build our first AI workflow!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🤖 Step 1.2: Import AI Model (Optional but Recommended)\n", "# We'll use Google's Gemini for real AI capabilities\n", "\n", "try:\n", "    from langchain_google_genai import ChatGoogleGenerativeAI\n", "    print(\"✅ Google AI imported successfully!\")\n", "    print(\"🎯 This enables real AI-powered nodes in our workflow\")\n", "    ai_available = True\n", "except ImportError:\n", "    print(\"⚠️ Google AI not available - we'll use simple functions instead\")\n", "    print(\"💡 To enable AI: pip install langchain-google-genai\")\n", "    ai_available = False\n", "\n", "print(f\"\\n🤖 AI Status: {'Available' if ai_available else 'Using fallback functions'}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔧 Step 2: Creating Simple Node Functions\n", "\n", "### 🎯 Why Start Simple?\n", "Before building complex AI workflows, let's understand the basics:\n", "- **Nodes are just Python functions**\n", "- **Each function does ONE specific task**\n", "- **Functions can be chained together automatically**\n", "\n", "### 💡 Real-World Assembly Line Analogy:\n", "```\n", "Raw Material → Worker 1 (adds \"from first function\") → Worker 2 (adds \"savita from second function\") → Final Product\n", "```\n", "\n", "### 🔍 What Each Function Does:\n", "- **function1**: Takes input, adds \" from first function\"\n", "- **function2**: Takes previous output, adds \" savita from second function\"\n", "- **Result**: Input gets processed by both functions in sequence"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🔧 Step 2.1: Define Our First Node Function\n", "def function1(input1):\n", "    \"\"\"\n", "    Our first node function - demonstrates basic data transformation\n", "    \n", "    What it does: Takes input and adds \" from first function\"\n", "    Why: Shows how nodes transform data\n", "    Real-world equivalent: Data preprocessing, initial analysis, input validation\n", "    \"\"\"\n", "    result = input1 + \" from first function\"\n", "    print(f\"🔵 Node 1 processed: '{input1}' → '{result}'\")\n", "    return result\n", "\n", "# Test the function independently\n", "print(\"🧪 Testing function1 independently:\")\n", "test_input = \"hello\"\n", "test_output = function1(test_input)\n", "print(f\"✅ Input: '{test_input}' became '{test_output}'\")\n", "\n", "print(\"\\n💡 Key Points:\")\n", "print(\"   • This is just a regular Python function\")\n", "print(\"   • It takes one input and returns one output\")\n", "print(\"   • LangGraph will call this function automatically\")\n", "print(\"   • In real apps, this could be AI analysis, web search, etc.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🔧 Step 2.2: Define Our Second Node Function\n", "def function2(input2):\n", "    \"\"\"\n", "    Our second node function - receives output from function1\n", "    \n", "    What it does: Takes input and adds \" savita from second function\"\n", "    Why: Demonstrates how nodes can be chained together\n", "    Real-world equivalent: Data analysis, content generation, final processing\n", "    \"\"\"\n", "    result = input2 + \" savita from second function\"\n", "    print(f\"🔵 Node 2 processed: '{input2}' → '{result}'\")\n", "    return result\n", "\n", "# Test the function independently\n", "print(\"🧪 Testing function2 independently:\")\n", "test_input = \"processed data\"\n", "test_output = function2(test_input)\n", "print(f\"✅ Input: '{test_input}' became '{test_output}'\")\n", "\n", "# Test manual chaining (what LangGraph will do automatically)\n", "print(\"\\n🔗 Manual chaining test (what LangGraph automates):\")\n", "step1_result = function1(\"sunny\")\n", "step2_result = function2(step1_result)\n", "print(f\"🎯 Final chained result: '{step2_result}'\")\n", "\n", "print(\"\\n💡 This manual chaining is exactly what LangGraph will do automatically!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🕸️ Step 3: Building Our First Graph\n", "\n", "### 🎯 What We're About to Do:\n", "Transform our manual function chaining into an **automated workflow**:\n", "\n", "**Manual Way** (what we just did):\n", "```python\n", "result1 = function1(input)\n", "result2 = function2(result1)\n", "```\n", "\n", "**LangGraph Way** (what we're building):\n", "```python\n", "app.invoke(input)  # Automatically runs function1 → function2\n", "```\n", "\n", "### 🏗️ Graph Building Process:\n", "1. **Create Graph**: Initialize empty workflow container\n", "2. **Add Nodes**: Register our functions as workflow steps\n", "3. **Add Edges**: Define how data flows between nodes\n", "4. **Set Entry Point**: Where the workflow starts\n", "5. **Set Exit Point**: Where the workflow ends\n", "6. **Compile**: Turn our definition into executable workflow\n", "\n", "### 💡 Benefits of Using LangGraph:\n", "- **Automatic execution**: No manual chaining needed\n", "- **Visualization**: See your workflow as a diagram\n", "- **Debugging**: Monitor each step individually\n", "- **Scalability**: Easy to add more nodes and complex logic"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🏗️ Step 3.1: Create the Graph Container\n", "# This is like creating an empty flowchart template\n", "\n", "workflow1 = Graph()\n", "\n", "print(\"🕸️ Graph container created!\")\n", "print(\"\\n💡 What we just did:\")\n", "print(\"   • Created an empty workflow container\")\n", "print(\"   • This will hold our nodes (functions) and connections\")\n", "print(\"   • Think of it as an empty flowchart waiting for steps\")\n", "\n", "print(f\"\\n📊 Graph object: {workflow1}\")\n", "print(\"\\n🎯 Next: We'll add our functions as nodes to this graph\")\n", "print(\"🔧 The graph is ready to receive our workflow definition!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🔵 Step 3.2: Add Nodes to the Graph\n", "# Convert our Python functions into graph nodes\n", "\n", "print(\"🔵 Adding nodes to the graph...\")\n", "\n", "# Add first function as a node\n", "workflow1.add_node(\"fun1\", function1)\n", "print(\"✅ Added Node 1:\")\n", "print(\"   • Node name: 'fun1' (this is how we'll reference it)\")\n", "print(\"   • Function: function1 (the actual code to execute)\")\n", "print(\"   • Purpose: First step in our workflow\")\n", "\n", "# Add second function as a node\n", "workflow1.add_node(\"fun2\", function2)\n", "print(\"\\n✅ Added Node 2:\")\n", "print(\"   • Node name: 'fun2' (unique identifier)\")\n", "print(\"   • Function: function2 (the processing logic)\")\n", "print(\"   • Purpose: Second step in our workflow\")\n", "\n", "print(\"\\n🎯 Important Concepts:\")\n", "print(\"   • Node names ('fun1', 'fun2') are like labels\")\n", "print(\"   • Functions (function1, function2) are the actual code\")\n", "print(\"   • Each node does ONE specific task\")\n", "print(\"   • Nodes are like workers in a factory assembly line\")\n", "\n", "print(\"\\n✅ Both nodes added successfully!\")\n", "print(\"🎯 Next: Connect these nodes with edges (data flow)\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ➡️ Step 3.3: Add Edges (Connections) Between Nodes\n", "# Define how data flows from one node to another\n", "\n", "workflow1.add_edge(\"fun1\", \"fun2\")\n", "\n", "print(\"➡️ Added Edge: fun1 → fun2\")\n", "print(\"\\n💡 What this edge means:\")\n", "print(\"   • Output from 'fun1' automatically becomes input to 'fun2'\")\n", "print(\"   • Data flows seamlessly between nodes\")\n", "print(\"   • Creates a pipeline: Input → fun1 → fun2 → Output\")\n", "print(\"   • No manual intervention needed\")\n", "\n", "print(\"\\n🔄 Complete Data Flow:\")\n", "print(\"   1. User provides initial input\")\n", "print(\"   2. fun1 processes it and produces output\")\n", "print(\"   3. fun1's output automatically goes to fun2\")\n", "print(\"   4. fun2 processes it and produces final output\")\n", "print(\"   5. fun2's output is returned to user\")\n", "\n", "print(\"\\n🏭 Assembly Line Analogy:\")\n", "print(\"   • Edge = Conveyor belt between workstations\")\n", "print(\"   • Product moves automatically from worker 1 to worker 2\")\n", "print(\"   • Each worker adds their contribution\")\n", "\n", "print(\"\\n✅ Edge added successfully!\")\n", "print(\"🎯 Next: Set entry and exit points for the workflow\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🚀 Step 3.4: Set Entry Point (Where the workflow starts)\n", "# Tell the graph which node should receive the initial input\n", "\n", "workflow1.set_entry_point(\"fun1\")\n", "\n", "print(\"🚀 Entry point set: 'fun1'\")\n", "print(\"\\n💡 What this means:\")\n", "print(\"   • When we run the workflow, it starts at 'fun1'\")\n", "print(\"   • The initial user input goes directly to 'fun1'\")\n", "print(\"   • This is like the 'START' button in a flowchart\")\n", "print(\"   • Ensures predictable workflow execution\")\n", "\n", "print(\"\\n🎯 Why we need an entry point:\")\n", "print(\"   • Graphs can have multiple nodes\")\n", "print(\"   • We need to specify where to begin execution\")\n", "print(\"   • Prevents confusion about workflow start\")\n", "print(\"   • Enables complex workflows with multiple entry points\")\n", "\n", "print(\"\\n🏁 Traffic Light Analogy:\")\n", "print(\"   • Entry point = Green light that starts the process\")\n", "print(\"   • Without it, cars (data) don't know when to go\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🏁 Step 3.5: Set Finish Point (Where the workflow ends)\n", "# Tell the graph which node produces the final output\n", "\n", "workflow1.set_finish_point(\"fun2\")\n", "\n", "print(\"🏁 Finish point set: 'fun2'\")\n", "print(\"\\n💡 What this means:\")\n", "print(\"   • The workflow ends after 'fun2' completes\")\n", "print(\"   • 'fun2's output becomes the final result\")\n", "print(\"   • This is like the 'END' flag in a flowchart\")\n", "print(\"   • Defines the workflow completion criteria\")\n", "\n", "print(\"\\n🎯 Why we need a finish point:\")\n", "print(\"   • Defines when the workflow is complete\")\n", "print(\"   • Specifies which node's output to return\")\n", "print(\"   • Prevents infinite loops or confusion\")\n", "print(\"   • Enables proper resource cleanup\")\n", "\n", "print(\"\\n🏆 Race Finish Line Analogy:\")\n", "print(\"   • Finish point = Finish line that ends the race\")\n", "print(\"   • Without it, runners don't know when to stop\")\n", "\n", "print(\"\\n✅ Graph structure complete!\")\n", "print(\"📊 Current workflow: START → fun1 → fun2 → END\")\n", "print(\"🎯 Next: Compile the graph into an executable application\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ⚙️ Step 3.6: Comp<PERSON> the Graph\n", "# Transform our graph definition into an executable application\n", "\n", "print(\"⚙️ Compiling the graph...\")\n", "app = workflow1.compile()\n", "\n", "print(\"✅ Graph compiled successfully!\")\n", "print(\"\\n💡 What compilation does:\")\n", "print(\"   • Validates the graph structure (checks for errors)\")\n", "print(\"   • Optimizes the execution path\")\n", "print(\"   • Creates an executable application\")\n", "print(\"   • Adds internal start/end nodes automatically\")\n", "print(\"   • Prepares the workflow for execution\")\n", "\n", "print(\"\\n🔍 Let's examine the compiled graph structure:\")\n", "try:\n", "    graph_info = app.get_graph()\n", "    print(f\"Graph nodes: {list(graph_info.nodes.keys())}\")\n", "    print(f\"Graph edges: {len(graph_info.edges)} connections\")\n", "except:\n", "    print(\"Graph structure created successfully\")\n", "\n", "print(\"\\n🎯 Notice the automatic additions:\")\n", "print(\"   • __start__: Internal entry point (added automatically)\")\n", "print(\"   • __end__: Internal exit point (added automatically)\")\n", "print(\"   • These handle the workflow lifecycle management\")\n", "\n", "print(\"\\n🏭 Factory Analogy:\")\n", "print(\"   • Compilation = Setting up the factory floor\")\n", "print(\"   • All machines (nodes) are connected and ready\")\n", "print(\"   • Quality control systems are in place\")\n", "print(\"   • Ready for production (execution)!\")\n", "\n", "print(\"\\n🚀 Workflow is now ready for execution!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 4}