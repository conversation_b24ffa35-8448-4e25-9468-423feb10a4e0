{"cells": [{"cell_type": "code", "execution_count": 1, "id": "bbc3cb43", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["all ok\n"]}], "source": ["print(\"all ok\")"]}, {"cell_type": "code", "execution_count": null, "id": "e6956d6b", "metadata": {}, "outputs": [], "source": ["graph()\n", "state\n", "edges\n", "nodes\n", "invoke\n", "stategraph()\n", "\n"]}, {"cell_type": "code", "execution_count": 34, "id": "a9f6e954", "metadata": {}, "outputs": [], "source": ["def function1(input1):\n", "    return input1+ \" from first function\""]}, {"cell_type": "code", "execution_count": 48, "id": "97d1f19e", "metadata": {}, "outputs": [], "source": ["def function2(input2):\n", "    return input2+ \" savita from second function\""]}, {"cell_type": "code", "execution_count": 49, "id": "15cef78c", "metadata": {}, "outputs": [], "source": ["def function3(input3):\n", "    pass"]}, {"cell_type": "code", "execution_count": 50, "id": "01edcb93", "metadata": {}, "outputs": [{"data": {"text/plain": ["'sunny from first function'"]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": ["function1(\"sunny\")"]}, {"cell_type": "code", "execution_count": 51, "id": "10399a00", "metadata": {}, "outputs": [{"data": {"text/plain": ["'savita savita from second function'"]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["function2(\"savita\")"]}, {"cell_type": "code", "execution_count": 52, "id": "7e01384d", "metadata": {}, "outputs": [], "source": ["from langgraph.graph import Graph"]}, {"cell_type": "code", "execution_count": 53, "id": "5c37aea4", "metadata": {}, "outputs": [], "source": ["workflow1=Graph()"]}, {"cell_type": "code", "execution_count": 54, "id": "e01c1895", "metadata": {}, "outputs": [{"data": {"text/plain": ["<langgraph.graph.graph.Graph at 0x1f15c715180>"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["workflow1.add_node(\"fun1\",function1)"]}, {"cell_type": "code", "execution_count": 55, "id": "bb380254", "metadata": {}, "outputs": [{"data": {"text/plain": ["<langgraph.graph.graph.Graph at 0x1f15c715180>"]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["workflow1.add_node(\"fun2\",function2)"]}, {"cell_type": "code", "execution_count": 56, "id": "e1e86bd4", "metadata": {}, "outputs": [{"data": {"text/plain": ["<langgraph.graph.graph.Graph at 0x1f15c715180>"]}, "execution_count": 56, "metadata": {}, "output_type": "execute_result"}], "source": ["workflow1.add_edge(\"fun1\",\"fun2\")"]}, {"cell_type": "code", "execution_count": 57, "id": "9498cc80", "metadata": {}, "outputs": [{"data": {"text/plain": ["<langgraph.graph.graph.Graph at 0x1f15c715180>"]}, "execution_count": 57, "metadata": {}, "output_type": "execute_result"}], "source": ["workflow1.set_entry_point(\"fun1\")"]}, {"cell_type": "code", "execution_count": 58, "id": "13c17aa0", "metadata": {}, "outputs": [{"data": {"text/plain": ["<langgraph.graph.graph.Graph at 0x1f15c715180>"]}, "execution_count": 58, "metadata": {}, "output_type": "execute_result"}], "source": ["workflow1.set_finish_point(\"fun2\")"]}, {"cell_type": "code", "execution_count": 59, "id": "d154dc22", "metadata": {}, "outputs": [], "source": ["app=workflow1.compile()"]}, {"cell_type": "code", "execution_count": 60, "id": "e3d2676b", "metadata": {}, "outputs": [{"data": {"text/plain": ["Graph(nodes={'fun1': Node(id='fun1', name='fun1', data=fun1(tags=None, recurse=True, explode_args=False, func_accepts_config=False, func_accepts={}), metadata=None), 'fun2': Node(id='fun2', name='fun2', data=fun2(tags=None, recurse=True, explode_args=False, func_accepts_config=False, func_accepts={}), metadata=None), '__start__': Node(id='__start__', name='__start__', data=None, metadata=None), '__end__': Node(id='__end__', name='__end__', data=None, metadata=None)}, edges=[Edge(source='__start__', target='fun1', data=None, conditional=False), Edge(source='fun1', target='fun2', data=None, conditional=False), Edge(source='fun2', target='__end__', data=None, conditional=False)])"]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["app.get_graph()"]}, {"cell_type": "code", "execution_count": 61, "id": "fc22b9a1", "metadata": {}, "outputs": [], "source": ["from IPython.display import Image, display"]}, {"cell_type": "code", "execution_count": 62, "id": "15d0ec3a", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAGoAAAFNCAIAAABnnW36AAAAAXNSR0IArs4c6QAAF5JJREFUeJztnXl4FEXegGvOzJmZTO77JoQAQiYQhKAJQbkSgRDADwV5Vj8XRFaX1XVdd9e44D646np+StTHRZGVCCxqQI7V5QgIBkjCIQkkIRfJTMjMZO6rp7u/P4YnG3Hu6sn0sPX+Ffqo/PJSXV1dXV0/BkmSABEozFAHEN4gfVAgfVAgfVAgfVAgfVCwIc9XdltNetxqwq1mHMfCow/E4jB4AhZPyBJJWPHpPJiiGIH1+7oum65fNnVeNIql7EgZhydk8YRMDjc86jJmJ6wmwmLC9WrMpHNk3yXKmijMKBAGUJTf+m722Y7tvonZiLyiyJwpImksJ4DfSh+0Q1h7s+HqOUMEn1m6PC42JcKv0/3Qh2PkiX8O9bSZi+fL8osjA4qWvvx4Wt94SJ01SXRvdazvZ/mqz2LE6z8YSB0nuLsiGiJIWoNj5Olv1Irrlor/TeKLWL6c4pM+tcJ+aLtiZmVM5sRAGojwovOi6cw3qgVrE2UJXO9Hk94warFPNnerBmxej7xjGOq37Xi526hzeD3Sy73SgZH1Hw6ULY+NTvThv+JOISaJe09V7P4PB3CHl0vTy8V76muVMJI9pVRKdYRhQNO/h20W4u5Fntp6T7VPp8KU3db/TncAgMI5UTfaLYZhh4djPOlr+FLl2f0dT/F8WcOXQx4OcKtPp8IwG5GUzQ9OYOFB2niBSYd7qIBu9bU3GwvuvtP6xgEwaZakvdngbq8HfYaMCWPdyystLVUqlf6etWvXrpdeeik4EYH0fEF7s9HdXtf6jFoHgwG4vDEdAujv7zca3QbqgdbW1iCEcwu+iOXACHfXr+sBq4HrFlmifw/PvkOS5M6dO7/55puenp7s7OwZM2asW7fu/Pnz69evBwBUVFSUl5e/8sorHR0de/fubWxsVCqV2dnZVVVVS5YsAQBcu3Zt1apVb731Vl1dnV6v53A4zc3NAID6+vpdu3bl5ORQHnB0QsRgr1UcJXL9x/yciw3aY3tuBqE/T5Ik+dlnn82aNau+vl6j0ezZs2fOnDk7duwgSfLEiRNyuVyhUDgPW7du3dKlSxsbG8+ePVtXVyeXy8+fP0+SZFdXl1wuX7t27c6dO69cuUKS5OrVq2tqaoIULUmS/64bvHRK63KX69pnMeE8gU/PzAHQ3NxcVFRUUVEBAFi2bNm0adPsdvvPD9u6davJZEpKSgIAFBUV7du379SpU4WFhc69M2fOXLVqVZAivA2egGUzEy53udbHYjHsDtcnwDNp0qT33ntv8+bNU6dOLSsrS0tLc3kYQRCff/75yZMn+/r6nFvGjRs3sjc/Pz9I4fmF65sDX8yyGPAg/crVq1c/99xzKpWqpqamvLy8pqZGo9HcdgxBEBs3bmxqanrqqaeOHz9+7ty5iRMnOncxGAwAAI8HNcjuFyaDQxDp+lp0XfsEYrbZ4OlhBQYmk1lVVVVVVdXZ2dnY2FhbW2u1Wrdu3Tr6mNbW1ra2ttraWrlc7tyi0+mcPzgf0sdybolZjwvErkW50SdiqQZctEeUsH///oKCgszMzOzs7OzsbLVa/e23345UKydOWdHRtx4Z29ra+vr6Jk+e7LLA0ScGg5t9VqGb2uf64pUlcCwmfHgwKAYPHDjw7LPPNjQ06PX6EydONDQ0TJkyBQCQkpICADhy5MiVK1eysrIYDMbOnTuNRmNXV9ebb75ZVFTkrkednJx86dKlc+fOabVayqNVDdhxBxnlbujU3d360HZF87HhYPQDFArFpk2b5HK5XC6fN2/etm3bTCaTc9cLL7xQXFy8YcMGkiQPHTpUXV0tl8urqqouX758+PBhuVz+8MMPOzsujY2NIwWePXt26dKl06dPd/ZsqOX8d5ojO5Tu9rod7+u8YDxzUL3qubRgXxp0hiTIHS/3zK6KzXTzGtPtY1nGRKHDTnZcMAUzPLpztcnIYDLS8wXuDnA7y4DFYpQsjjlzUJ0zWchguqiA/f39Dz30kMtzmUwmQbjuNlZXVz/55JO+Be83Tz/9dEtLi8tdUqnUXcu4ZcuWkpKSn28nCLLxoHp2VSzT1Z/vxMtg/Z63bqSOExQvkLkqnTCZXNdNq9Xqrl/G4XCC12Uzm8047rq7imEYh+P6jT6fz2ezXVSj7+vV/Z3m5U+nevqVnhtOnQr74PnOrh9NlDfJNKfzovGD5zt1aszzYV6GpCKj2QsfTTzymVKtCFY3kIaoFfbvdg1WPp4UKfMyhcr7iF5yNr90Wezed270XjVTFyF96Wk17337Rml1XEKG90bG10ka/Z2Wg39XTJ8XPXm2hIogaUrzUe35bzWLHktKzPSpgfZjipBeg331/oA4in3vstio+DvtrblaYTu+d8hswB/4ZVKkzNdpY/5NUMMx8scz+uZjw6m5gqxJwuQcPiciPOb0ucNuJfo7LV2XTH3t5sKyqEkl/l1bAU6PvH7Z1NFs7GkzRco4sgSuNJYTFcf1cVZSyDEbce1Nu/Ymphm06zVYRr4wZ6rI3XOFZwLUN4Kiy6pR2nUqTDtkt7oZkg0YtVo9etyFKnhCpjSGK4nlRCdwfbk/eABWX1Cpra1lMBiPP/54qANxS3i3XCEH6YMC6YMC6YMC6YMC6YMC6YMC6YMC6YMC6YMC6YMC6YMC6YMC6YMC6YMC6YMC6YMC6YMC6YMC6YMC6YMC6YMC6YMC6YMC6YMC6YMC6YMC6YMC6YMC6YMC6YMC6YMC6YMC6YMC6YMC6YMC6YMC6YMC6YMC6YOCjp/FVFZWEgRBkqTza3WxWEwQBIPBOHDgQKhDux3YjAnBIDEx8ezZsyzWrS/knBKnTZsW6rhcQMeLd82aNVFRUaO3SCSSRx55JHQRuYWO+kpKSvLy8kZvycnJmTFjRugicgsd9QEAVq1aJZHc+rRWIpGsWbMm1BG5hqb6Zs+ePbJaX25u7qxZs0IdkWtoqm+kAtK21XNC8Z0Xx8ibN2wETkFnKCuxqCBrNgAgLXZKf4cFvkAmi+HjAg++Q1m/r7/T8sNBjV6NCSVseq4aRpKkUeuIjObMrIimyiM1+k4fUHdeMJZUJUQHbbliqlAN2Br2KvPkYpcrS/kLBW1ff6fl8ve6eb9Ipb87AEBMUsTCR1MvndIquqzwpVGgr+WoVj43hsen713oNiIEzKlzYlqODcMXRcHfrFba4tPDLLFCQgafksVZKdCnVzsio8MsW1tkNEenwuDLoeiKo92ojReoGmYKmwaLniB9UCB9UCB9UCB9UCB9UCB9UCB9UCB9UCB9UCB9UIRG36c7PqpeMb9ycSlMIQaj4ZlnnygrL+rq6qQuNP8IwSwDq9X69+3bFsx/YP68yoALabt65cWaZ3m8EA+UhaD2WSxmAMCMGSWTJ08NuJDtn9TeN3fhpqd/Pwa5ijww1vpu3Oitqr4fAPBizW8XVswGAMxfOOuL3Z+NHLDlL3/Y+NSjAIDr1zvKyouutbf9/g+/LisvenBVxYcfvTvyZuaJdb9+7NENIX8nNdb6UlLS9u4+DACoefGVb/Y3eDjSmV7jtdc2z7u/4l+Hz/xm0x/+8fn2hpNHnXvT0jJCknTsNuh753XWrNLS++69p5zNZk8rmhEXF3/tWhCToQYAffU561Re3oSRLSKR2Gh0m2k4JNBd3+jWjYYzOUOvj8Fg/MSRmxxR9CT0+jgc7uhLsqe3K6Th+Efo9U2YMKnh5FGz2QwA+OTTD31p3QiCaG4519xyrrPzGgCg7eqPzS3nrrReHpN4f0Lo5zZvfPLZ11/fsqjyHi6X++DKNbNL5rS2eRFht9s3/WbdyD//+uqfnV2ZT/6+J/jx/gQKpgi9++uOR17MAXScVOUWkgSfvtTx5BuwicxDf/GGNUgfFEgfFEgfFEgfFEgfFEgfFEgfFEgfFEgfFEgfFEgfFEgfFBTpC6vhFgAA4SBZbAqCpkBfTBJXo7TBlzOWaFV2GRUJrqnQlxLR22qCL2cs6W01xqVS8AUeBfqK5sram3VqRdhUQLXC1nlBX1ge5cOxXqDmg1S1wv6vHcrMyZEp44R0/kBLr8b6rpq6L+vvX50gS6Dg4qXsc2gcI5uODve0mpXdFHzoGSQSM3lp4wWFZVEsDjU3OzquIjQCSq59h4P0QYH0QYH0QYH0QYH0QYH0QYH0QYH0QYH0QYH0QYH0QYH0QYH0QYH0QYH0QYH0QYH0QYH0QYH0QYH0QYH0QYH0QYH0QYH0QYH0QYH0QYH0QYH0QYH0QYH0QYH0QYH0QYH0QYH0QYH0QYH0QYH0QYH0QYH0QYH0QUHHr4pWrlzZ2fmThcBJkszKytq9e3fognINHWvf8uXLIyJ+8rUoj8d76KGHQheRW+ior7q6OjU1dfSW1NTUJUuWhC4it9BRHwBgxYoVPN6tHLBcLnfFihWhjsg1NNW3ZMmS5ORk58/p6elVVVWhjsg1NNXHZDJXrlwZERFB56pH0zvvCE5xX3zxRagDcQuVX5Of//dwbxutvyZPyOCljRdMn0dBXmgn1OjTKO2HP1VmhcNaBr1txu4fDfPXJERRsRAJBfowO1n3am/JsjBITO5ENWD7/kvlg8+kwa9oQMGto/GQOr1AHC7unOnJU8eLGo9o4IuiQN+Na5bU8UL4csaStHxR31UzfDkU6Bvqt4VR1XMijeGqFfTITQ7CMLk2k83AHRQETdNuc7iA9EGB9EGB9EGB9EGB9EGB9EGB9EGB9EGB9EGB9EGB9EERmjR3n+746Ov6PTabtf6rY4GVYDAa3n7nrxcvNun1utzc8YsfWF4+Zx7VYXonXHOT19T8tn+g78kNz4hE4kOH67e8/EJsTBxMtu7ACIE++NzkLS3nm5rPvvv2xwUFkwEAkyZOOXXqWMPJo2OvLyxzk0+ePHX7x7tHMh+z2ez4+ETn/8oYE5a5yZlMZnp6Jpt969Lp7e3u7r6emzt+jP6GUdD3zutjbnKCIF7725aEhKQF8x8Y+yDpq8+X3ORms/n53z81PKx5+82PuFwK3tv6S+jz87rDa25yhXLgd8//iiTJ1199Pzo6JhQx0kBfYLnJzWbz757/lVAoeuP12tvmUo4lob94A8tN/trrm5lM5l+2vBFCd7Sofc7c5CtXrBEIBM7c5Hy+wPMply61HD32r3W/fKqr+z9ToHk8fv74guDH+xNCry+A3OTOA7bVvjV6Y2Zm9scf1QU52NtBucmhCH3bF9YgfVAgfVAgfVAgfVAgfVAgfVAgfVAgfVAgfVAgfVAgfVD8lybXZlAUMAX6JDEcvQajIpixQ6/GpLEUfHtHRXLt5IjBbgt8OWOJstsSk0yP5NpT7pU2faeymX16R0EHrBai+TtV4RwKkmtToC8xkzehWHLo4z6NMgzSk6sGbIc+6ps4S0JJanfKPodubdSf2DvE5TFFURwGRS0zQZIAACZFpZEkaRjG7FaitDpu/DQxJWVS/DG+ToWZ9DhJUFNmfX09AKCyMvCJWKNhMBkiCYvar7UpflUkieFIYiiLjyEYZjAYyTl8qgqkHNRthgLpgwLpgwLpgwLpgwLpgwLpgwLpgwLpgwLpgwLpgwLpgwLpgwLpgwLpgwLpgwLpgwLpgwLpgwLpgwLpgwLpgwLpgwLpgwLpgwLpgwLpgwLpgwLpgwLpgwLpgwLpgwLpgwLpgwLpgwLpgwLpgwLpgwLpgwLpg4KOKT4XLVqkUChu25iUlLR///4QReQWOta+RYsWMX/GggULQh2XC+ior7q6Oi0tbfSWjIyMlStXhi4it9BRX1xc3Ny5c0dvKSsri4kJzeKunqGjPgDAsmXLMjIynD+npaUtX7481BG5hqb64uPjS0tLnT/fd999cXFxoY7INTTV58ysnZGRkZaWVl1dHepY3EJBx8Wkc3RcMOrUDosBt5pwm42yntDNwZsAgLh4yqpeRASDJ2QJxKzIaHbOXSKhBPZz5sD14RjZdFR7rdmgV2PSRCE7gsPistgcFotN3xqNOwgHhuMY7jBj2kFTZDQ3f5rortnSgNNEB6jvWpOxYd8QR8iNSowUx3lZZpm26G+atQo9ZrLPXho7rlAUQAl+67NZiP0fKnVaPCFHJojiBfAr6YZJYxnsGJbIWA88nsiJ8K8a+qdPr3HsfadfKBPF5Uj9j5PWDHYMW7WmpRuSI2V+NIh+6BvstX713kBsjiwqmZpVPOiG5oZh6LqmakNybIqvS7z42sybdI76DxQJeTF3qjsAgCxFnJAX8/W2AZMe9/EUn/Q57MS+/xuITBRHJgjhIqQ7knihOFH85Xv9PmYu90nfmYPDJIsdl0XBok/0Jy4rCifZPxzS+HKwd30mHX7ljC6pgKaPTcEguSD2x9N6k87h9Ujv+o7/c0iWJmGxwm2FQwhYHKY0SdzwldrrkV70WU1E31VzdKqEutioRKsbfOaPxZdbT1BecnSatOeK2Wrycg/xoq/jgiEqWcz4b6p6TphshjRReP2y0cthnne3t5j4UvquwBVU+FJ+R4uXzINeetiqflv2zGA9mekN6q8PvtHdexHDbOPHzbyv9NGY6BQAQMPpuqMNO3659p3tnz83pOpJTMgtK1ldeNetBKjNF48c+q7WajVOGD/7nrsfDFJsAABhNL/rBy/Nn6fa58BINofJZAblysVx/P2P13f3Xly++IVnNn7OixC+/cEvhrVKAACbzbVY9fsOvPZg1Z9e2/xD/rhZdfv+bDBqAACKwY5/7PnT9MLK3z29Z+qk+/cdeD0YsTlhsRgMJvCc98yTPsOwg80J1uhTV0/LkKrnf5bV5OUWi0WyxQs3RXD5J8984czchmG2BXPXp6dOBABMl1fiuGNA0Q4A+P6HvTJpUvm9a/l88bic6dMKK4IUnhM2h2Uc9rQosCc7xmGMETR93b0XuBxedmbhrTiYzMz0KR3Xz49kA0xNvpVekRchAgBYrAYAwJC6Nz4+a6SQ1OT8IIV3Kyo2wzDsqffnpe0j8WC9RLdYjXbM+swfi0dvjBTHAGceq1HpFUe3HWazXiT8z8MPlxP025rni9eTPr6Y7bAHazlhsSiaFyFcu+rV0RuZLJbns/h8sR2zjvzTZjMFKTwnDhshEHsKyZM+gZiFWX0de/CXxIQcq80UJU2IliU7t6g0NyJFXl7mRkkTrrafIQiCyWQCAFqvnQpSeE4wi0MY6Umfp6ZNIGLZrbjDHhSDeTnF43KKv/jyZa1u0Ggabjhd9+b7j5y/cNDzWZMLyg1G9YEj75Ik2d559vTZfcGIzYnDjjswgicItPYBBohNiTCoLFFJgbwH8Mpjq9889cPuHXUv9PRdiovJKJYvvnvaUs+nTMibVTFv4+nGfx4/tVMWlfRg1Z/e/3g9CM4sJ8NNc2wKz3M2Ay+jzc1HtW3N1sT8WOqjoz2KKzcnTOPfdY+n1xJe+iU5U0TDChMenOuXzjis+LDSnDvVy9C6l46LOIqdni9Q9eric2QuD8Bxx4tb57mOwGFns7guK39SfO4Tj23z/Kv94o8vzyWB68uIIHAm00X7lZZS8Pgjb7srUN2rzZoo9Hzb9elVkV7j+MfWntxZqSyu67I0wwMut1utRh7PdaPJYnEkkVQ2CO5iAADYMRuX4+LVD5vNvdXN/BkOK95+uu/h59PFUV6ql09v2o7vHbrRaU+aGE/VWv50hiTJGxeUmRN4JYu9T4nz6ZlsZmU0m0WourVUhEd3hjqHeTxyxkLXjdVt+KSPw2UueSLZpjPrB4Pbyw85eqUJM1kWr0/2cazEj9fkFiP+5TZFhFggS6Pp2D0k6h4tZrIsWZfEE/o6UOLfJA3cQR7crjQaGPHjYhjBGQcMCSRBKtqGpDLGvNXxLLYff1cgM6zOHRm+fEYflx0jkN0RU4RUlqEuzcSZ4qK5fr/IDnCCmnYIazqqVSscXIlAGMVnu+nT0BmHHTdrLFadOTaZPbVUGljeMajZpQ6M7G41X2syaRR2wGSwOCwGm+UcC6EnBEGQDhzHcJIgY5K4eYXCrElQ004o+6rIqHVohzCdCvPl5XxoYABhJFsSw5HGckRSarIM0fGjrDCCvhdaWID0QYH0QYH0QYH0QYH0QfH/5u3PZrlPpd0AAAAASUVORK5CYII=", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display(Image(app.get_graph().draw_mermaid_png()))"]}, {"cell_type": "code", "execution_count": 63, "id": "363f212f", "metadata": {}, "outputs": [{"data": {"text/plain": ["'hi this is sunny from first function savita from second function'"]}, "execution_count": 63, "metadata": {}, "output_type": "execute_result"}], "source": ["app.invoke(\"hi this is sunny\")"]}, {"cell_type": "code", "execution_count": 65, "id": "e84c550d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["here is output from fun1\n", "_______\n", "hi this is rohit from first function\n", "\n", "\n", "here is output from fun2\n", "_______\n", "hi this is rohit from first function savita from second function\n", "\n", "\n"]}], "source": ["for output in app.stream(\"hi this is rohit\"):\n", "    for key,value in output.items():\n", "        print(f\"here is output from {key}\")\n", "        print(\"_______\")\n", "        print(value)\n", "        print(\"\\n\")\n", "    "]}, {"cell_type": "code", "execution_count": 80, "id": "a2705963", "metadata": {}, "outputs": [], "source": [" from langchain_google_genai import ChatGoogleGenerativeAI\n", "model=ChatGoogleGenerativeAI(model='gemini-1.5-flash')"]}, {"cell_type": "code", "execution_count": 82, "id": "e71fecfc", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Hi there! How can I help you today?'"]}, "execution_count": 82, "metadata": {}, "output_type": "execute_result"}], "source": ["model.invoke(\"hi\").content"]}, {"cell_type": "code", "execution_count": 83, "id": "1c11b65a", "metadata": {}, "outputs": [], "source": ["def llm(input):\n", "    from langchain_google_genai import ChatGoogleGenerativeAI\n", "    model=ChatGoogleGenerativeAI(model='gemini-1.5-flash')\n", "    output=model.invoke(input)\n", "    return output.content"]}, {"cell_type": "code", "execution_count": 84, "id": "07be4106", "metadata": {}, "outputs": [], "source": ["def token_counter(input):\n", "    token=input.split()\n", "    token_number=len(token)\n", "    return f\"total token number in the generated answer is {token_number}\""]}, {"cell_type": "code", "execution_count": 85, "id": "54872a36", "metadata": {}, "outputs": [], "source": ["workflow2=Graph()"]}, {"cell_type": "code", "execution_count": 86, "id": "4cb4d7cf", "metadata": {}, "outputs": [{"data": {"text/plain": ["<langgraph.graph.graph.Graph at 0x1f15ea49960>"]}, "execution_count": 86, "metadata": {}, "output_type": "execute_result"}], "source": ["workflow2.add_node(\"My_LLM\",llm)"]}, {"cell_type": "code", "execution_count": 87, "id": "62df1dd3", "metadata": {}, "outputs": [{"data": {"text/plain": ["<langgraph.graph.graph.Graph at 0x1f15ea49960>"]}, "execution_count": 87, "metadata": {}, "output_type": "execute_result"}], "source": ["workflow2.add_node(\"LLM_Output_Token_Counter\",token_counter)"]}, {"cell_type": "code", "execution_count": 88, "id": "e8a4cccc", "metadata": {}, "outputs": [{"data": {"text/plain": ["<langgraph.graph.graph.Graph at 0x1f15ea49960>"]}, "execution_count": 88, "metadata": {}, "output_type": "execute_result"}], "source": ["workflow2.add_edge(\"My_LLM\",\"LLM_Output_Token_Counter\")"]}, {"cell_type": "code", "execution_count": 89, "id": "993ae266", "metadata": {}, "outputs": [{"data": {"text/plain": ["<langgraph.graph.graph.Graph at 0x1f15ea49960>"]}, "execution_count": 89, "metadata": {}, "output_type": "execute_result"}], "source": ["workflow2.set_entry_point(\"My_LLM\")"]}, {"cell_type": "code", "execution_count": 90, "id": "5e7d269e", "metadata": {}, "outputs": [{"data": {"text/plain": ["<langgraph.graph.graph.Graph at 0x1f15ea49960>"]}, "execution_count": 90, "metadata": {}, "output_type": "execute_result"}], "source": ["workflow2.set_finish_point(\"LLM_Output_Token_Counter\")"]}, {"cell_type": "code", "execution_count": 91, "id": "59f30084", "metadata": {}, "outputs": [], "source": ["app=workflow2.compile()"]}, {"cell_type": "code", "execution_count": 92, "id": "7c5715fe", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display(Image(app.get_graph().draw_mermaid_png()))"]}, {"cell_type": "code", "execution_count": 93, "id": "b509b51e", "metadata": {}, "outputs": [{"data": {"text/plain": ["'total token number in the generated answer is 80'"]}, "execution_count": 93, "metadata": {}, "output_type": "execute_result"}], "source": ["app.invoke(\"can you tell me about the india's capital?\")"]}, {"cell_type": "code", "execution_count": 94, "id": "e3320744", "metadata": {}, "outputs": [{"data": {"text/plain": ["'total token number in the generated answer is 638'"]}, "execution_count": 94, "metadata": {}, "output_type": "execute_result"}], "source": ["app.invoke(\"tell me about the tata enterpirse in very detail.\")"]}, {"cell_type": "code", "execution_count": 95, "id": "98f8aff1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["here is output from My_LLM\n", "_______\n", "The Tata Group is a massive Indian multinational conglomerate, one of the largest and oldest in the country.  Understanding its complexity requires looking at several key aspects:\n", "\n", "**I. History and Structure:**\n", "\n", "* **Origins:** Founded in 1868 by <PERSON><PERSON><PERSON>, the group started as a trading company.  Its early successes in textiles and steel laid the foundation for its future diversification.  The philosophy of <PERSON><PERSON><PERSON>, emphasizing ethical conduct, social responsibility, and long-term vision, continues to be a guiding principle.\n", "* **Structure:**  Unlike a typical corporation with a centralized management, the Tata Group is a complex network of independent operating companies, each with its own board and management.  These companies are held together through a network of shareholdings, often involving Tata Sons, the group's holding company.  This structure allows for greater autonomy and flexibility within individual businesses while maintaining a sense of overall group identity.\n", "* **Leadership:**  The Tata Sons board plays a critical role in overall group strategy, appointments, and oversight.  The chairman of Tata Sons holds a powerful position within the group.  Succession planning has historically been a significant focus, often involving a careful selection process.\n", "* **Trusts:**  A significant portion of Tata Sons is held by philanthropic trusts, which ensures a commitment to social responsibility and prevents the group from being solely driven by profit maximization.  These trusts influence the group's direction and investment decisions, promoting long-term sustainability and community development.\n", "\n", "**II. Key Businesses and Sectors:**\n", "\n", "The Tata Group's presence spans numerous sectors, making it a truly diversified conglomerate.  Some of its most prominent businesses include:\n", "\n", "* **Automotive:** Tata Motors is a global leader in commercial vehicles and a major player in passenger cars, both in India and internationally.  Jaguar Land Rover is a significant subsidiary acquired in 2008.\n", "* **Steel:** Tata Steel is one of the world's largest steel producers, with operations in India and other countries.\n", "* **IT Services:** Tata Consultancy Services (TCS) is a leading global IT services and consulting company, known for its large workforce and significant revenue generation.  Tata Elxsi specializes in design and technology services.\n", "* **Consumer Goods:** Tata Consumer Products is a major player in the food and beverage industry in India, including brands like Tata Tea, Tetley, and Biscuits.\n", "* **Chemicals:** Tata Chemicals is involved in various chemical products, including fertilizers and salt.\n", "* **Energy:** Tata Power is a significant player in the Indian power sector, involved in generation, transmission, and distribution.\n", "* **Telecommunications:** Tata Teleservices (though significantly restructured) historically had a presence in the telecommunications sector.\n", "* **Hospitality:** Indian Hotels Company Limited (IHCL), which operates the Taj Hotels chain, is a prominent player in the hospitality industry.\n", "* **Aerospace & Defence:** Tata Advanced Systems is involved in aerospace and defense technologies.\n", "* **Retail:** Tata operates several retail businesses across diverse sectors.\n", "\n", "**III. Global Presence:**\n", "\n", "While originating in India, the Tata Group has a significant international presence.  Through its various subsidiaries, it operates in numerous countries across Asia, Europe, Africa, and North America.  Jaguar Land Rover, TCS, and Tata Steel are examples of businesses with substantial global operations.\n", "\n", "**IV. Social Responsibility and Philanthropy:**\n", "\n", "The Tata Group's commitment to social responsibility is deeply ingrained in its culture.  The significant role of the Tata Trusts in the group's ownership structure ensures that social impact remains a key consideration in business decisions.  The group engages in numerous philanthropic initiatives focusing on education, healthcare, and community development.\n", "\n", "**V. Challenges and Future Outlook:**\n", "\n", "Despite its size and success, the Tata Group faces several challenges:\n", "\n", "* **Competition:** Intense competition in various sectors requires continuous innovation and adaptation.\n", "* **Economic fluctuations:**  Global and Indian economic conditions impact the performance of its diverse businesses.\n", "* **Governance and leadership transitions:**  Maintaining effective governance and a smooth succession process are crucial for long-term success.\n", "* **Technological disruption:**  Adapting to rapid technological advancements is necessary across all its sectors.\n", "* **Sustainability:**  Balancing business growth with environmental sustainability is a growing concern.\n", "\n", "The future of the Tata Group will depend on its ability to navigate these challenges while continuing to innovate, adapt, and uphold its commitment to ethical business practices and social responsibility.  It's a complex and evolving entity, and its continued success will depend on its ability to balance its diverse interests and maintain a cohesive vision.\n", "\n", "\n", "here is output from LLM_Output_Token_Counter\n", "_______\n", "total token number in the generated answer is 706\n", "\n", "\n"]}], "source": ["for output in app.stream(\"tell me about the tata enterpirse in very detail.\"):\n", "    for key,value in output.items():\n", "        print(f\"here is output from {key}\")\n", "        print(\"_______\")\n", "        print(value)\n", "        print(\"\\n\")"]}, {"cell_type": "code", "execution_count": null, "id": "cde3746a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}