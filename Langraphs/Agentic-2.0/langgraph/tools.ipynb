{"cells": [{"cell_type": "code", "execution_count": 1, "id": "4341d919", "metadata": {}, "outputs": [], "source": ["from langchain_community.tools import WikipediaQueryRun"]}, {"cell_type": "code", "execution_count": 2, "id": "3c7f01e1", "metadata": {}, "outputs": [], "source": ["from langchain_community.utilities import WikipediaAPIWrapper"]}, {"cell_type": "code", "execution_count": 3, "id": "2fdd72bc", "metadata": {}, "outputs": [], "source": ["api_wrapper=WikipediaAPIWrapper(top_k_results=5,doc_content_chars_max= 500)"]}, {"cell_type": "code", "execution_count": 4, "id": "694ea1a8", "metadata": {}, "outputs": [], "source": ["wiki_tool=WikipediaQueryRun(api_wrapper=api_wrapper)"]}, {"cell_type": "code", "execution_count": 5, "id": "8a450b4b", "metadata": {}, "outputs": [{"data": {"text/plain": ["'wikipedia'"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["wiki_tool.name"]}, {"cell_type": "code", "execution_count": 6, "id": "0f46f450", "metadata": {}, "outputs": [{"data": {"text/plain": ["'A wrapper around Wikipedia. Useful for when you need to answer general questions about people, places, companies, facts, historical events, or other subjects. Input should be a search query.'"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["wiki_tool.description"]}, {"cell_type": "code", "execution_count": 7, "id": "0aa4502c", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'query': {'description': 'query to look up on wikipedia',\n", "  'title': 'Query',\n", "  'type': 'string'}}"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["wiki_tool.args"]}, {"cell_type": "code", "execution_count": 8, "id": "99477475", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"Page: <PERSON><PERSON>\\nSummary: <PERSON><PERSON> ( EE-lon; born June 28, 1971) is a businessman known for his leadership of Tesla, SpaceX, X (formerly Twitter) and DOGE. <PERSON><PERSON> has been considered the wealthiest person in the world since 2021; as of May 2025, Forbes estimates his net worth to be US$424.7 billion. \\nBorn to a wealthy family in Pretoria, South Africa, <PERSON><PERSON> emigrated in 1989 to Canada. He received bachelor's degrees from the University of Pennsylvania in 1997 before moving to California, Unit\""]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["wiki_tool.run({\"query\":\"elon musk\"})"]}, {"cell_type": "code", "execution_count": 9, "id": "bf3a63ed", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Complete_Content2\\Agentic-2.0\\env\\lib\\site-packages\\wikipedia\\wikipedia.py:389: GuessedAtParserWarning: No parser was explicitly specified, so I'm using the best available HTML parser for this system (\"html.parser\"). This usually isn't a problem, but if you run this code on another system, or in a different virtual environment, it may use a different parser and behave differently.\n", "\n", "The code that caused this warning is on line 389 of the file c:\\Complete_Content2\\Agentic-2.0\\env\\lib\\site-packages\\wikipedia\\wikipedia.py. To get rid of this warning, pass the additional argument 'features=\"html.parser\"' to the BeautifulSoup constructor.\n", "\n", "  lis = BeautifulSoup(html).find_all('li')\n"]}, {"data": {"text/plain": ["\"Page: Royal Challengers Bengaluru\\nSummary: Royal Challengers Bengaluru, formerly known as Royal Challengers Bangalore, commonly known as RCB, is a professional T20 franchise cricket team based in Bengaluru, Karnataka, that competes in the Indian Premier League. Founded in 2008 by United Spirits, the team's home ground is M. Chinnaswamy Stadium. RCB won their first title in 2025. The team has also finished as the runners-up on three occasions: in 2009, 2011, and 2016. They have also qualified for\""]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["wiki_tool.run(\"RCB\")"]}, {"cell_type": "code", "execution_count": 10, "id": "a79c6cc6", "metadata": {}, "outputs": [], "source": ["from langchain_community.tools import YouTubeSearchTool"]}, {"cell_type": "code", "execution_count": 11, "id": "c35d1810", "metadata": {}, "outputs": [], "source": ["tool=YouTubeSearchTool()"]}, {"cell_type": "code", "execution_count": 12, "id": "3dfe8f0e", "metadata": {}, "outputs": [{"data": {"text/plain": ["'youtube_search'"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["tool.name"]}, {"cell_type": "code", "execution_count": 13, "id": "31dd4376", "metadata": {}, "outputs": [{"data": {"text/plain": ["'search for youtube videos associated with a person. the input to this tool should be a comma separated list, the first part contains a person name and the second a number that is the maximum number of video results to return aka num_results. the second part is optional'"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["tool.description"]}, {"cell_type": "code", "execution_count": 15, "id": "ed05eed1", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"['https://www.youtube.com/watch?v=-GdWEiJsVDA&pp=ygUMc3Vubnkgc2F2aXRh', 'https://www.youtube.com/watch?v=oFMixPMJ6Ko&pp=ygUMc3Vubnkgc2F2aXRh']\""]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["tool.run(\"sunny savita\")"]}, {"cell_type": "code", "execution_count": 16, "id": "7ab2dde0", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"['https://www.youtube.com/watch?v=JxgmHe2NyeY&pp=ygUKa3Jpc2ggbmFpaw%3D%3D', 'https://www.youtube.com/watch?v=p4pHsuEf4Ms&pp=ygUKa3Jpc2ggbmFpaw%3D%3D']\""]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["tool.run(\"krish naik\")"]}, {"cell_type": "code", "execution_count": 17, "id": "21d809df", "metadata": {}, "outputs": [], "source": ["from langchain_community.tools.tavily_search import TavilySearchResults"]}, {"cell_type": "code", "execution_count": null, "id": "16ed7cac", "metadata": {}, "outputs": [], "source": ["import os\n", "TAVILY_API_KEY=os.getenv(\"TAVILY_API_KEY\")"]}, {"cell_type": "code", "execution_count": null, "id": "f745e41b", "metadata": {}, "outputs": [], "source": ["tool=TavilySearchResults(tavily_api_key=TAVILY_API_KEY)"]}, {"cell_type": "code", "execution_count": 20, "id": "42cf9b63", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'title': \"Bengaluru stampede case: What we know so far on RCB's ...\",\n", "  'url': 'https://timesofindia.indiatimes.com/sports/cricket/ipl/top-stories/bengaluru-stampede-case-what-we-know-so-far-on-rcbs-celebrations-that-turned-catastrophic/articleshow/121670873.cms',\n", "  'content': '[Follow us](https://news.google.com/publications/CAAqBwgKMM6y_Qowwu70Ag)\\n\\nRoyal Challengers Bengaluru\\'s IPL victory celebrations turned tragic as a stampede at M Chinnaswamy Stadium resulted in 11 deaths and 75 injuries. FIRs have been filed against RCB, events organisers DNA, and Karnataka State Cricket Association for alleged negligence. The Karnataka High Court has sought a report from the government while arrests have been and officials suspended.\\n\\nRead More [...] [](https://timesofindia.indiatimes.com/city/bengaluru/celebrations-turn-tragic-bloodbath-at-rcbs-maiden-ipl-trophy-victory-day-to-remember-forever-see-pics/photostory/121646177.cms)[Celebrations turn tragic: Bloodbath at RCB’s maiden IPL trophy victory, day to remember forever | see pics Lakhs of fans had gathered around Chinnaswamy Stadium, celebrating RCB’s historic first-ever IPL title with joy and excitement.Times Of [...] India](https://timesofindia.indiatimes.com/city/bengaluru/celebrations-turn-tragic-bloodbath-at-rcbs-maiden-ipl-trophy-victory-day-to-remember-forever-see-pics/photostory/121646177.cms \"Celebrations turn tragic: Bloodbath at RCB’s maiden IPL trophy victory, day to remember forever | see pics\")',\n", "  'score': 0.8455478},\n", " {'title': 'Chinnaswamy Stadium Stampede: What triggered the deadly chaos ...',\n", "  'url': 'https://m.economictimes.com/news/bengaluru-news/chin<PERSON><PERSON>-stadium-stampede-what-triggered-the-chaos-that-turned-deadly-in-rcbs-victory-celebration/articleshow/121624517.cms',\n", "  'content': \"![Image 3](https://img.etimg.com/thumb/msid-121625936,width-300,height-225,imgsize-121910,resizemode-75/.jpg)\\n\\nRCB's IPL victory celebrations turn tragic: Stampede in Bengaluru; 11 dead, 33 injured\\n\\nThe celebrations after RCB took a heart-breaking turn when a deadly [stampede](https://m.economictimes.com/topic/stampede) near the M Chinnaswamy Stadium killed over 11 fans dead and nearly 33 others injured. [...] Karnataka chief minister <PERSON><PERSON><PERSON><PERSON> on Wednesday said the state government was deeply saddened by the stampede that claimed 11 lives and left 33 injured during Royal Challengers Bengaluru’s IPL victory celebrations at Chinnaswamy Stadium.\\n\\n“This tragedy should not have happened. The government expresses deep sorrow. Most of the deceased are youth,” <PERSON><PERSON><PERSON><PERSON> said at a press conference in Bengaluru, flanked by deputy CM <PERSON> and home minister <PERSON>. [...] Anticipating massive crowds, the Karnataka government had earlier cancelled an open bus victory parade from Vidhana Soudha to Chinnaswamy Stadium, citing security concerns, a warning that proved prescient.\\n\\nIn Video: [RCB's IPL victory celebrations turn tragic: Stampede in Bengaluru; 11 dead, 33 injured](https://m.economictimes.com/news/india/rcbs-ipl-victory-celebrations-turn-tragic-stampede-in-bengaluru-at-least-7-dead-over-50-injured/videoshow/121625936.cms)\",\n", "  'score': 0.83710164},\n", " {'title': \"Several Killed as RCB's Victory Celebration Turns Deadly - YouTube\",\n", "  'url': 'https://www.youtube.com/watch?v=gz8chxY7elU',\n", "  'content': \"At least 11 people were killed and 30 injured in a stampede outside the Chinnaswamy cricket stadium in Bengaluru. The incident happened during the Royal Challengers' victory celebrations after their first IPL triumph in 18 years. Did the state government underestimate the rush? Were crowd control measures not in place? <PERSON><PERSON><PERSON> tells you.\\n\\n--- [...] It was supposed to be a day of pure joy, of celebration and pride. Instead, Bengaluru is in shock today. I'm sure you've seen the news and the pictures. Bengaluru was all decked up for a massive celebration today. Their IPL team had finally won the tournament. The Royal Challengers beat Punjab Kings yesterday. It was their first IPL trophy in 18 years. Now the RCB fans are a very passionate lot. Plus they've been waiting for a win since 2008. So they packed the streets of Bengaluru in [...] soon the fan frenzy turned fatal. The exact details are not clear yet but reports say the rush led to a stampede. At least 11 people were killed, including children, and more than 30 were injured. Like I said, the exact sequence of events is unclear, but these pictures give you an idea. Uh you can see fans climbing up fences and trees. In some places, the police pushed back with force. We will show you the pictures, but as always, viewer discretion is advised. [Applause] [Music] [Applause] Such\",\n", "  'score': 0.83290404},\n", " {'title': 'Bengaluru stampede: A sea of cheers drowned out by wailing sirens',\n", "  'url': 'https://www.thehindu.com/news/cities/bangalore/bengaluru-stampede-a-sea-of-cheers-drowned-out-by-wailing-sirens/article69660074.ece',\n", "  'content': 'What began as a day of rapturous celebration over\\xa0the IPL victory of Royal Challengers Bengaluru (RCB)\\xa0turned into one of crushing tragedy in Bengaluru on June 4, 2025 evening, when 11 people — all aged below 40 — died and scores of others were injured when a massive build up of fans at the M. Chinnaswamy Stadium led to chaos and stampede. [...] ![Slippers after the RCB (Royal Challenge Bengaluru) victory celebration at Vidhana Soudha in Bengaluru on Thursday, 05 June 2024.](https://www.thehindu.com/theme/images/th-online/1x1_spacer.png \"Slippers after the RCB (Royal Challenge Bengaluru) victory celebration at Vidhana Soudha in Bengaluru on Thursday, 05 June 2024.\") [...] ![Karnataka Chief Minister <PERSON><PERSON><PERSON><PERSON> visits a person injured in the stampede during the RCB team’s IPL victory celebrations, at a hospital in Bengaluru on June 4, 2025. ](https://www.thehindu.com/theme/images/th-online/1x1_spacer.png \"Karnataka Chief Minister <PERSON><PERSON><PERSON><PERSON> visits a person injured in the stampede during the RCB team’s IPL victory celebrations, at a hospital in Bengaluru on June 4, 2025. \")',\n", "  'score': 0.8029425},\n", " {'title': '3 feared dead in stampede at RCB victory celebrations in Bengaluru',\n", "  'url': 'https://www.reddit.com/r/Cricket/comments/1l33sse/3_feared_dead_in_stampede_at_rcb_victory/',\n", "  'content': \"A tragic day for Indian cricket — over 15 fans lost their lives during RCB's victory parade, not in protest or play, but in celebration. This\",\n", "  'score': 0.7785319}]"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["tool.invoke({\"query\":\"what happend in RCB victory celebration?\"})"]}, {"cell_type": "code", "execution_count": null, "id": "377e6c26", "metadata": {}, "outputs": [], "source": ["! pip install youtube-search-python\n", "! pip install youtube-search\n"]}, {"cell_type": "markdown", "id": "c1e3cd34", "metadata": {}, "source": ["## Custom_tool"]}, {"cell_type": "code", "execution_count": 21, "id": "0058a3af", "metadata": {}, "outputs": [], "source": ["def multiply(a:int,b:int)->int:\n", "    return a*b"]}, {"cell_type": "code", "execution_count": 22, "id": "2fd727eb", "metadata": {}, "outputs": [{"data": {"text/plain": ["200"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["multiply(10,20)"]}, {"cell_type": "code", "execution_count": 23, "id": "913cc79c", "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'function' object has no attribute 'run'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[23], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[43mmultiply\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrun\u001b[49m(\u001b[38;5;241m10\u001b[39m,\u001b[38;5;241m20\u001b[39m)\n", "\u001b[1;31mAttributeError\u001b[0m: 'function' object has no attribute 'run'"]}], "source": ["multiply.run(10,20)"]}, {"cell_type": "code", "execution_count": 24, "id": "c8864bb2", "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'function' object has no attribute 'invoke'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[24], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[43mmultiply\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43minvoke\u001b[49m(\u001b[38;5;241m10\u001b[39m,\u001b[38;5;241m20\u001b[39m)\n", "\u001b[1;31mAttributeError\u001b[0m: 'function' object has no attribute 'invoke'"]}], "source": ["multiply.invoke(10,20)"]}, {"cell_type": "code", "execution_count": 27, "id": "3a3665bc", "metadata": {}, "outputs": [], "source": ["from langchain.agents import tool\n", "@tool\n", "def multiply(a:int,b:int)->int:\n", "    '''this tool is for the multiplication'''\n", "    return a*b"]}, {"cell_type": "code", "execution_count": 31, "id": "50306c92", "metadata": {}, "outputs": [{"data": {"text/plain": ["200"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["multiply.invoke({\"a\":10,\"b\":20})"]}, {"cell_type": "code", "execution_count": 32, "id": "5ca800ff", "metadata": {}, "outputs": [{"data": {"text/plain": ["'multiply'"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["multiply.name"]}, {"cell_type": "code", "execution_count": 33, "id": "8978a8c9", "metadata": {}, "outputs": [{"data": {"text/plain": ["'this tool is for the multiplication'"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["multiply.description"]}, {"cell_type": "code", "execution_count": 34, "id": "6a41b532", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'a': {'title': 'A', 'type': 'integer'},\n", " 'b': {'title': 'B', 'type': 'integer'}}"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["multiply.args"]}, {"cell_type": "code", "execution_count": 35, "id": "6de152ea", "metadata": {}, "outputs": [], "source": ["def get_word_length(word:str)->int:\n", "    return len(word)"]}, {"cell_type": "code", "execution_count": 36, "id": "a0fffdcf", "metadata": {}, "outputs": [{"data": {"text/plain": ["12"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["get_word_length(\"sunny savita\")"]}, {"cell_type": "code", "execution_count": 37, "id": "6a65a42f", "metadata": {}, "outputs": [{"data": {"text/plain": ["12"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["get_word_length(\"naredra modi\")"]}, {"cell_type": "code", "execution_count": 39, "id": "93dccd5e", "metadata": {}, "outputs": [], "source": ["@tool\n", "def get_word_length(word:str)->int:\n", "    \"\"\"this funtion is calculating a length of the word\"\"\"\n", "    return len(word)"]}, {"cell_type": "code", "execution_count": 40, "id": "1292db4b", "metadata": {}, "outputs": [{"data": {"text/plain": ["'get_word_length'"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["get_word_length.name"]}, {"cell_type": "code", "execution_count": 41, "id": "309a3ab7", "metadata": {}, "outputs": [{"data": {"text/plain": ["'this funtion is calculating a length of the word'"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["get_word_length.description"]}, {"cell_type": "code", "execution_count": 43, "id": "375b13d8", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'word': {'title': 'Word', 'type': 'string'}}"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["get_word_length.args"]}, {"cell_type": "code", "execution_count": 44, "id": "fcdfeefe", "metadata": {}, "outputs": [{"data": {"text/plain": ["5"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["get_word_length(\"sunny\")"]}, {"cell_type": "code", "execution_count": 45, "id": "da04955e", "metadata": {}, "outputs": [{"data": {"text/plain": ["5"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["get_word_length.invoke(\"sunny\")"]}, {"cell_type": "code", "execution_count": null, "id": "87bb9160", "metadata": {}, "outputs": [], "source": ["@tool\n", "def call_gamil_api(args):\n", "    \"\"\"this is my gamil api calling funtion\"\"\"\n", "    pass"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}