{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🕸️ LangGraph Fundamentals - Every Concept Explained\n", "\n", "## 🤔 What Problem Does LangGraph Solve?\n", "\n", "**Traditional AI Applications Problem:**\n", "```\n", "User Input → AI Model → Single Response\n", "```\n", "- **Linear**: No decision making or branching\n", "- **Static**: Can't adapt based on intermediate results\n", "- **Limited**: Hard to build complex reasoning\n", "\n", "**LangGraph Solution:**\n", "```\n", "User Input → Node 1 → Decision → Node 2A or Node 2B → Final Output\n", "```\n", "- **Graph-based**: AI can choose different paths\n", "- **Dynamic**: Decisions based on intermediate results\n", "- **Complex**: Multi-step reasoning and workflows\n", "\n", "## 🎯 Real-World Examples\n", "\n", "### 🔍 **Research Assistant**\n", "```\n", "Question → Search Web → Analyze Results → Need More Info? → Search Again → Summarize\n", "```\n", "\n", "### 🎯 **Customer Support**\n", "```\n", "Ticket → Classify Issue → Simple? → Auto-Resolve : Escalate → Human Review\n", "```\n", "\n", "### 📝 **Content Creation**\n", "```\n", "Topic → Research → Outline → Write → Review → Good? → Publish : Revise\n", "```\n", "\n", "## 🧩 Core LangGraph Concepts\n", "\n", "### 1. **Graph** 🕸️\n", "- **What**: The overall workflow structure\n", "- **Why**: Organizes how different steps connect\n", "- **Problem Solved**: No more rigid, linear AI workflows\n", "\n", "### 2. **Nodes** 🔵\n", "- **What**: Individual functions or AI operations\n", "- **Why**: Each node does one specific task\n", "- **Problem Solved**: Break complex tasks into manageable pieces\n", "\n", "### 3. **Edges** ➡️\n", "- **What**: Connections between nodes\n", "- **Why**: Define the flow from one step to another\n", "- **Problem Solved**: Control the order of operations\n", "\n", "### 4. **State** 📊\n", "- **What**: Information that flows between nodes\n", "- **Why**: Nodes need to share data and context\n", "- **Problem Solved**: Maintain context across multiple steps\n", "\n", "### 5. **Conditional Logic** 🤔\n", "- **What**: Decision points that choose different paths\n", "- **Why**: AI needs to make smart decisions\n", "- **Problem Solved**: Dynamic workflows that adapt\n", "\n", "Let's see these concepts in action! 🚀"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🛠️ Step 1: Import LangGraph Components\n", "# Let's understand what each import does and why we need it\n", "\n", "from langgraph.graph import Graph  # The main graph structure\n", "from IPython.display import Image, display  # To visualize our graphs\n", "\n", "print(\"✅ LangGraph imported successfully!\")\n", "print(\"\\n🎯 What we just imported:\")\n", "print(\"   • Graph: The main container for our workflow\")\n", "print(\"   • Image/display: Tools to visualize our graph structure\")\n", "print(\"\\n💡 Think of Graph like a flowchart that AI can follow!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔧 Step 2: Creating Simple Functions (Nodes)\n", "\n", "### 🎯 Why Start with Simple Functions?\n", "Before we build complex AI workflows, let's understand the basics:\n", "- **Nodes are just Python functions**\n", "- **Each function does ONE specific task**\n", "- **Functions can be chained together**\n", "\n", "### 💡 Real-World Analogy:\n", "Think of an assembly line:\n", "- **Worker 1**: Adds \"from first function\" to input\n", "- **Worker 2**: Adds \"savita from second function\" to input\n", "- **Result**: Input gets processed by both workers in sequence"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🔧 Step 2.1: Define Our First Node Function\n", "# This is the simplest possible node - it just modifies the input\n", "\n", "def function1(input1):\n", "    \"\"\"\n", "    Our first node function\n", "    \n", "    What it does: Takes input and adds \" from first function\"\n", "    Why: Demonstrates how nodes transform data\n", "    Real-world equivalent: Data preprocessing, initial analysis\n", "    \"\"\"\n", "    result = input1 + \" from first function\"\n", "    print(f\"🔵 Node 1 processed: '{input1}' → '{result}'\")\n", "    return result\n", "\n", "# Test the function by itself\n", "test_input = \"hello\"\n", "test_output = function1(test_input)\n", "print(f\"\\n✅ Function 1 test: '{test_input}' became '{test_output}'\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🔧 Step 2.2: Define Our Second Node Function\n", "# This function will receive the output from function1\n", "\n", "def function2(input2):\n", "    \"\"\"\n", "    Our second node function\n", "    \n", "    What it does: Takes input and adds \" savita from second function\"\n", "    Why: Shows how nodes can be chained together\n", "    Real-world equivalent: Data analysis, content generation\n", "    \"\"\"\n", "    result = input2 + \" savita from second function\"\n", "    print(f\"🔵 Node 2 processed: '{input2}' → '{result}'\")\n", "    return result\n", "\n", "# Test the function by itself\n", "test_input = \"processed data\"\n", "test_output = function2(test_input)\n", "print(f\"\\n✅ Function 2 test: '{test_input}' became '{test_output}'\")\n", "\n", "# Test chaining them manually\n", "print(\"\\n🔗 Manual chaining test:\")\n", "step1 = function1(\"sunny\")\n", "step2 = function2(step1)\n", "print(f\"Final result: '{step2}'\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🕸️ Step 3: Building Our First Graph\n", "\n", "### 🎯 What We're About to Do:\n", "Instead of manually chaining functions, we'll create a **graph** that:\n", "1. **Automatically manages** the flow between functions\n", "2. **Handles data passing** between nodes\n", "3. **Provides visualization** of the workflow\n", "4. **Enables complex routing** (we'll see this later)\n", "\n", "### 🏗️ Graph Building Steps:\n", "1. **Create Graph**: Initialize the workflow container\n", "2. **Add Nodes**: Register our functions as workflow steps\n", "3. **Add Edges**: Define how data flows between nodes\n", "4. **Set Entry Point**: Where the workflow starts\n", "5. **Set Exit Point**: Where the workflow ends\n", "6. **Compile**: Turn our definition into an executable workflow"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🏗️ Step 3.1: Create the Graph Container\n", "# This is like creating an empty flowchart\n", "\n", "workflow1 = Graph()\n", "\n", "print(\"🕸️ Graph created!\")\n", "print(\"\\n💡 What we just did:\")\n", "print(\"   • Created an empty workflow container\")\n", "print(\"   • This will hold our nodes and connections\")\n", "print(\"   • Think of it as an empty flowchart waiting for steps\")\n", "\n", "print(f\"\\n📊 Graph object: {workflow1}\")\n", "print(\"🎯 Next: We'll add our functions as nodes to this graph\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🔵 Step 3.2: Add Nodes to the Graph\n", "# Convert our Python functions into graph nodes\n", "\n", "# Add first function as a node\n", "workflow1.add_node(\"fun1\", function1)\n", "print(\"🔵 Added Node 1: 'fun1' → function1\")\n", "print(\"   • Node name: 'fun1' (this is how we'll reference it)\")\n", "print(\"   • Function: function1 (the actual code to execute)\")\n", "\n", "# Add second function as a node\n", "workflow1.add_node(\"fun2\", function2)\n", "print(\"\\n🔵 Added Node 2: 'fun2' → function2\")\n", "print(\"   • Node name: 'fun2' (unique identifier)\")\n", "print(\"   • Function: function2 (the processing logic)\")\n", "\n", "print(\"\\n✅ Both nodes added to the graph!\")\n", "print(\"💡 Nodes are like workers in a factory - each has a specific job\")\n", "print(\"🎯 Next: We need to connect these nodes with edges\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ➡️ Step 3.3: Add Edges (Connections) Between Nodes\n", "# Define how data flows from one node to another\n", "\n", "workflow1.add_edge(\"fun1\", \"fun2\")\n", "\n", "print(\"➡️ Added Edge: fun1 → fun2\")\n", "print(\"\\n💡 What this edge means:\")\n", "print(\"   • Output from 'fun1' becomes input to 'fun2'\")\n", "print(\"   • Data flows automatically between nodes\")\n", "print(\"   • Creates a pipeline: Input → fun1 → fun2 → Output\")\n", "\n", "print(\"\\n🔄 Data Flow:\")\n", "print(\"   1. User provides input\")\n", "print(\"   2. fun1 processes it\")\n", "print(\"   3. fun1's output goes to fun2\")\n", "print(\"   4. fun2 processes it\")\n", "print(\"   5. fun2's output is the final result\")\n", "\n", "print(\"\\n✅ Edge added successfully!\")\n", "print(\"🎯 Next: Set entry and exit points\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🚀 Step 3.4: Set Entry Point (Where the workflow starts)\n", "# Tell the graph which node should receive the initial input\n", "\n", "workflow1.set_entry_point(\"fun1\")\n", "\n", "print(\"🚀 Entry point set: 'fun1'\")\n", "print(\"\\n💡 What this means:\")\n", "print(\"   • When we run the workflow, it starts at 'fun1'\")\n", "print(\"   • The initial user input goes directly to 'fun1'\")\n", "print(\"   • This is like the 'START' in a flowchart\")\n", "\n", "print(\"\\n🎯 Why we need an entry point:\")\n", "print(\"   • Graphs can have multiple nodes\")\n", "print(\"   • We need to specify where to begin\")\n", "print(\"   • Ensures predictable workflow execution\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🏁 Step 3.5: Set Finish Point (Where the workflow ends)\n", "# Tell the graph which node produces the final output\n", "\n", "workflow1.set_finish_point(\"fun2\")\n", "\n", "print(\"🏁 Finish point set: 'fun2'\")\n", "print(\"\\n💡 What this means:\")\n", "print(\"   • The workflow ends after 'fun2' completes\")\n", "print(\"   • 'fun2's output becomes the final result\")\n", "print(\"   • This is like the 'END' in a flowchart\")\n", "\n", "print(\"\\n🎯 Why we need a finish point:\")\n", "print(\"   • Defines when the workflow is complete\")\n", "print(\"   • Specifies which node's output to return\")\n", "print(\"   • Prevents infinite loops or confusion\")\n", "\n", "print(\"\\n✅ Graph structure complete!\")\n", "print(\"🎯 Next: Compile the graph into an executable workflow\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ⚙️ Step 3.6: Comp<PERSON> the Graph\n", "# Transform our graph definition into an executable application\n", "\n", "app = workflow1.compile()\n", "\n", "print(\"⚙️ Graph compiled successfully!\")\n", "print(\"\\n💡 What compilation does:\")\n", "print(\"   • Validates the graph structure\")\n", "print(\"   • Optimizes the execution path\")\n", "print(\"   • Creates an executable application\")\n", "print(\"   • Adds internal start/end nodes automatically\")\n", "\n", "print(\"\\n🔍 Let's examine the compiled graph:\")\n", "graph_info = app.get_graph()\n", "print(f\"Graph structure: {graph_info}\")\n", "\n", "print(\"\\n🎯 Notice the automatic additions:\")\n", "print(\"   • __start__: Internal entry point\")\n", "print(\"   • __end__: Internal exit point\")\n", "print(\"   • These handle the workflow lifecycle\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📊 Step 4: Visualizing Our Graph\n", "\n", "### 🎯 Why Visualization Matters:\n", "- **See the workflow structure** at a glance\n", "- **Debug complex graphs** more easily\n", "- **Communicate workflows** to team members\n", "- **Understand data flow** visually\n", "\n", "### 🖼️ What You'll See:\n", "- **Boxes**: Represent nodes (our functions)\n", "- **Arrows**: Show data flow direction\n", "- **Start/End**: Automatic workflow boundaries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 📊 Step 4.1: Generate and Display Graph Visualization\n", "# Create a visual representation of our workflow\n", "\n", "try:\n", "    # Generate the graph visualization\n", "    graph_image = app.get_graph().draw_mermaid_png()\n", "    display(Image(graph_image))\n", "    \n", "    print(\"📊 Graph visualization displayed above!\")\n", "    print(\"\\n🔍 What you're seeing:\")\n", "    print(\"   • __start__ → fun1 → fun2 → __end__\")\n", "    print(\"   • Clear data flow from left to right\")\n", "    print(\"   • Each box is a processing step\")\n", "    print(\"   • Arrows show the execution order\")\n", "    \n", "except Exception as e:\n", "    print(f\"⚠️ Visualization error: {e}\")\n", "    print(\"💡 Graph structure (text format):\")\n", "    print(\"   __start__ → fun1 → fun2 → __end__\")\n", "    print(\"\\n🎯 This shows our simple linear workflow\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🚀 Step 5: Running Our Graph\n", "\n", "### 🎯 Two Ways to Execute:\n", "1. **invoke()**: Run the entire workflow and get final result\n", "2. **stream()**: See step-by-step execution with intermediate results\n", "\n", "### 💡 When to Use Each:\n", "- **invoke()**: When you only care about the final output\n", "- **stream()**: When you want to see what happens at each step (debugging, monitoring)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🚀 Step 5.1: Simple Execution with invoke()\n", "# Run the entire workflow and get the final result\n", "\n", "print(\"🚀 Testing workflow with invoke()...\")\n", "print(\"\\n\" + \"=\"*50)\n", "\n", "# Test with different inputs\n", "test_inputs = [\"hi this is sunny\", \"hello world\", \"testing langgraph\"]\n", "\n", "for i, test_input in enumerate(test_inputs, 1):\n", "    print(f\"\\n🧪 Test {i}:\")\n", "    print(f\"Input: '{test_input}'\")\n", "    \n", "    result = app.invoke(test_input)\n", "    print(f\"Output: '{result}'\")\n", "    \n", "    print(\"\\n🔍 What happened:\")\n", "    print(f\"   1. '{test_input}' → fun1 → '{test_input} from first function'\")\n", "    print(f\"   2. '{test_input} from first function' → fun2 → '{result}'\")\n", "\n", "print(\"\\n\" + \"=\"*50)\n", "print(\"✅ All tests completed successfully!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🔍 Step 5.2: Step-by-Step Execution with stream()\n", "# See what happens at each node during execution\n", "\n", "print(\"🔍 Testing workflow with stream() for detailed view...\")\n", "print(\"\\n\" + \"=\"*60)\n", "\n", "test_input = \"hi this is rohit\"\n", "print(f\"📥 Input: '{test_input}'\")\n", "print(\"\\n🔄 Step-by-step execution:\")\n", "\n", "# Stream the execution to see each step\n", "for step_number, output in enumerate(app.stream(test_input), 1):\n", "    print(f\"\\n📍 Step {step_number}:\")\n", "    \n", "    for node_name, node_output in output.items():\n", "        print(f\"   🔵 Node: {node_name}\")\n", "        print(f\"   📤 Output: '{node_output}'\")\n", "        \n", "        # Explain what this step accomplished\n", "        if node_name == \"fun1\":\n", "            print(f\"   💡 Explanation: Added ' from first function' to input\")\n", "        elif node_name == \"fun2\":\n", "            print(f\"   💡 Explanation: Added ' savita from second function' to previous output\")\n", "        \n", "        print(\"   \" + \"-\"*40)\n", "\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"✅ Streaming execution completed!\")\n", "print(\"\\n💡 Key Insights:\")\n", "print(\"   • Each node processes data independently\")\n", "print(\"   • Output from one node becomes input to the next\")\n", "print(\"   • We can monitor the workflow in real-time\")\n", "print(\"   • Perfect for debugging complex workflows\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🤖 Step 6: Adding AI to Our Graph\n", "\n", "### 🎯 Why Add AI?\n", "So far we've used simple string manipulation. Let's add **real AI** to see <PERSON><PERSON><PERSON><PERSON>'s power:\n", "- **LLM Integration**: Use Google Gemini for text generation\n", "- **Token Counting**: Analyze the AI's output\n", "- **Real Workflow**: Input → AI Processing → Analysis → Output\n", "\n", "### 🧠 What We'll Build:\n", "```\n", "User Question → AI Model → Generated Answer → Token Counter → Final Report\n", "```\n", "\n", "This demonstrates how LangGraph can orchestrate **real AI workflows**!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🤖 Step 6.1: Create AI-Powered Node Function\n", "# This function will use Google's Gemini AI model\n", "\n", "def llm_node(input_text):\n", "    \"\"\"\n", "    AI-powered node that generates responses using Google Gemini\n", "    \n", "    What it does: Takes user input and generates AI response\n", "    Why: Demonstrates real AI integration in workflows\n", "    Real-world use: Question answering, content generation, analysis\n", "    \"\"\"\n", "    try:\n", "        from langchain_google_genai import ChatGoogleGenerativeAI\n", "        \n", "        # Initialize the AI model\n", "        model = ChatGoogleGenerativeAI(model='gemini-1.5-flash')\n", "        \n", "        print(f\"🤖 AI processing: '{input_text}'\")\n", "        \n", "        # Generate AI response\n", "        response = model.invoke(input_text)\n", "        ai_output = response.content\n", "        \n", "        print(f\"🧠 AI generated: '{ai_output[:100]}...'\")  # Show first 100 chars\n", "        return ai_output\n", "        \n", "    except Exception as e:\n", "        print(f\"⚠️ AI model error: {e}\")\n", "        # Fallback response if AI fails\n", "        fallback = f\"I received your message: '{input_text}'. This is a fallback response since AI is not available.\"\n", "        print(f\"🔄 Using fallback: '{fallback}'\")\n", "        return fallback\n", "\n", "# Test the AI function\n", "print(\"🧪 Testing AI function:\")\n", "test_result = llm_node(\"What is machine learning?\")\n", "print(f\"\\n✅ AI test result: '{test_result[:150]}...\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 📊 Step 6.2: Create Analysis Node Function\n", "# This function will analyze the AI's output\n", "\n", "def token_counter_node(ai_response):\n", "    \"\"\"\n", "    Analysis node that counts tokens and provides statistics\n", "    \n", "    What it does: Analyzes AI output for length, complexity, etc.\n", "    Why: Demonstrates post-processing and analysis\n", "    Real-world use: Quality control, cost tracking, content analysis\n", "    \"\"\"\n", "    # Simple token counting (split by spaces)\n", "    tokens = ai_response.split()\n", "    token_count = len(tokens)\n", "    \n", "    # Additional analysis\n", "    char_count = len(ai_response)\n", "    sentence_count = ai_response.count('.') + ai_response.count('!') + ai_response.count('?')\n", "    \n", "    # Create analysis report\n", "    analysis = f\"\"\"📊 AI Response Analysis:\n", "   • Token count: {token_count} tokens\n", "   • Character count: {char_count} characters\n", "   • Estimated sentences: {sentence_count}\n", "   • Average words per sentence: {token_count/max(sentence_count, 1):.1f}\n", "   \n", "🤖 Original AI Response:\n", "{ai_response}\"\"\"\n", "    \n", "    print(f\"📊 Analysis complete: {token_count} tokens, {char_count} characters\")\n", "    return analysis\n", "\n", "# Test the analysis function\n", "print(\"🧪 Testing analysis function:\")\n", "test_analysis = token_counter_node(\"This is a test response with multiple words and sentences.\")\n", "print(f\"\\n✅ Analysis test completed\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 4}