# ===============================================================================
# 🔑 VECTOR DATABASE LEARNING PROJECT - API KEYS CONFIGURATION
# ===============================================================================
#
# INSTRUCTIONS:
# 1. Copy this file to .env (remove .template from filename)
# 2. Replace placeholder values with your actual API keys
# 3. Never commit the .env file to version control (it's in .gitignore)
# 4. Restart Jupyter notebook after adding keys
#
# ===============================================================================

# 🤗 HUGGING FACE TOKEN
# ===============================================================================
# PURPOSE: Access pre-trained embedding models and datasets
# PROBLEM SOLVED: Download and use state-of-the-art embedding models locally
# USED IN: All modules for text embeddings (sentence-transformers models)
# COST: FREE
# HOW TO GET:
#   1. Visit https://huggingface.co
#   2. Create free account
#   3. Go to Settings → Access Tokens
#   4. Create new token with "Read" permissions
# MODELS USED: all-MiniLM-L6-v2, all-mpnet-base-v2, sentence-transformers
# FALLBACK: Some models work without token but with rate limits
HF_TOKEN=*************************************

# 🌟 OPENAI API KEY
# ===============================================================================
# PURPOSE: Access GPT models and OpenAI's text-embedding-ada-002
# PROBLEM SOLVED: High-quality embeddings and LLM responses for RAG systems
# USED IN: Module 2 (embeddings comparison), Module 5 (RAG with ChatGPT)
# COST: Pay-per-use (~$0.0001 per 1K tokens for embeddings, ~$0.002 per 1K for GPT-3.5)
# HOW TO GET:
#   1. Visit https://platform.openai.com
#   2. Create account and add payment method
#   3. Go to API Keys section
#   4. Create new secret key
# MODELS USED: text-embedding-ada-002 (1536 dimensions), gpt-3.5-turbo, gpt-4
# FALLBACK: Use HuggingFace embeddings and Google AI for generation
OPENAI_API_KEY=********************************************************************************************************************************************************************

# 🎯 GOOGLE AI API KEY (Gemini)
# ===============================================================================
# PURPOSE: Access Google's Gemini models and embedding-001
# PROBLEM SOLVED: Free alternative to OpenAI for LLM responses and embeddings
# USED IN: Module 2 (embeddings comparison), Module 5 (RAG with Gemini)
# COST: FREE tier with generous limits (60 requests/minute)
# HOW TO GET:
#   1. Visit https://makersuite.google.com
#   2. Create Google account if needed
#   3. Generate API key
#   4. No payment method required for free tier
# MODELS USED: gemini-pro (text generation), embedding-001 (768 dimensions)
# FALLBACK: Use HuggingFace for embeddings, skip LLM parts if no other keys
GOOGLE_API_KEY=AIzaSyCzTA4biBzqLgY-paZxGv-HjUwLW8aZTvs

# 🌲 PINECONE API KEY
# ===============================================================================
# PURPOSE: Cloud-based vector database for production-scale applications
# PROBLEM SOLVED: Managed vector storage, real-time updates, global distribution
# USED IN: Module 4 (cloud vector database), Module 6 (production deployment)
# COST: FREE tier (1 index, 5M vectors, 2 replicas)
# HOW TO GET:
#   1. Visit https://www.pinecone.io
#   2. Create free account
#   3. Go to API Keys in dashboard
#   4. Copy your API key
# FEATURES: Serverless architecture, metadata filtering, real-time updates
# FALLBACK: Use FAISS for local vector storage (covered in Module 3)
PINECONE_API_KEY=pcsk_4zPtHR_NJWGtLH3jVKHCxs3gPJBG36D8MVNHgeZSkjUrdgahaMbqaDF78d4Kkqy8zu325Y

# ===============================================================================
# 🔧 OPTIONAL API KEYS (Advanced Use Cases)
# ===============================================================================
# These are not required for the core learning path but enable additional features

# 🧠 ANTHROPIC API KEY (Claude)
# ===============================================================================
# PURPOSE: Access Claude models for advanced RAG responses
# PROBLEM SOLVED: Alternative LLM with different strengths (longer context, safety)
# USED IN: Optional advanced RAG implementations
# COST: Pay-per-use (similar to OpenAI pricing)
# HOW TO GET:
#   1. Visit https://console.anthropic.com
#   2. Create account and add payment method
#   3. Generate API key
# NOTE: Not covered in main curriculum but can be substituted for OpenAI
ANTHROPIC_API_KEY=************************************************************************************************************

# 🔮 COHERE API KEY
# ===============================================================================
# PURPOSE: Access Cohere's embedding and generation models
# PROBLEM SOLVED: Enterprise-grade NLP APIs with good multilingual support
# USED IN: Optional embedding model comparisons
# COST: FREE tier available, then pay-per-use
# HOW TO GET:
#   1. Visit https://cohere.ai
#   2. Create account
#   3. Go to API Keys section
# NOTE: Good for production applications requiring enterprise support
COHERE_API_KEY=iU2mablcI1ngQPPbo23aE6XPedrzecb2nbwRLOKE

# ===============================================================================
# 📊 PRIORITY GUIDE - Which Keys Do You Need?
# ===============================================================================
#
# 🚀 ESSENTIAL (Start Here):
#   ✅ HF_TOKEN - Required for all embedding models (FREE)
#
# 🎯 RECOMMENDED (Full Experience):
#   ✅ HF_TOKEN - For embeddings (FREE)
#   ✅ GOOGLE_API_KEY - For LLM responses (FREE tier)
#   ✅ PINECONE_API_KEY - For cloud vector DB (FREE tier)
#
# 🌟 COMPLETE (All Features):
#   ✅ All above keys
#   ✅ OPENAI_API_KEY - For premium embeddings/LLM (PAID)
#
# 💡 BUDGET-FRIENDLY PATH:
#   - Use only HF_TOKEN and GOOGLE_API_KEY
#   - Skip Pinecone (use FAISS instead)
#   - Skip OpenAI (use Google AI instead)
#
# ===============================================================================
# 🔍 WHAT EACH KEY ENABLES IN THE LEARNING PROJECT
# ===============================================================================
#
# 📓 MODULE 1 - Vector Database Fundamentals:
#   • HF_TOKEN: Download sentence-transformers for embeddings
#   • No other keys required - works with local models
#
# 📓 MODULE 2 - Embeddings and Models:
#   • HF_TOKEN: Multiple HuggingFace embedding models
#   • OPENAI_API_KEY: OpenAI text-embedding-ada-002 comparison
#   • GOOGLE_API_KEY: Google embedding-001 comparison
#
# 📓 MODULE 3 - FAISS Implementation:
#   • HF_TOKEN: Embeddings for local vector search
#   • No cloud services needed - all local processing
#
# 📓 MODULE 4 - Pinecone Cloud Database:
#   • HF_TOKEN: Generate embeddings to store in Pinecone
#   • PINECONE_API_KEY: Create and manage cloud vector indexes
#
# 📓 MODULE 5 - RAG Implementation:
#   • HF_TOKEN: Document embeddings for retrieval
#   • OPENAI_API_KEY: ChatGPT for answer generation
#   • GOOGLE_API_KEY: Gemini as alternative for generation
#   • PINECONE_API_KEY: Cloud vector storage for documents
#
# 📓 MODULE 6 - Production Deployment:
#   • All keys: Demonstrate production patterns with different providers
#   • Focus on monitoring, scaling, and optimization
#
# ===============================================================================
# 💰 COST BREAKDOWN (Approximate)
# ===============================================================================
#
# 🆓 FREE TIER USAGE (Recommended for Learning):
#   • HF_TOKEN: Completely free
#   • GOOGLE_API_KEY: 60 requests/minute free
#   • PINECONE_API_KEY: 5M vectors free
#   • Total Cost: $0
#
# 💵 PAID USAGE (For Advanced Features):
#   • OPENAI_API_KEY: ~$1-5 for entire course
#   • ANTHROPIC_API_KEY: ~$1-5 for entire course
#   • COHERE_API_KEY: ~$1-3 for entire course
#
# 📊 ESTIMATED TOTAL COST FOR COMPLETE EXPERIENCE: $5-15
#
# ===============================================================================
# 🛡️ SECURITY BEST PRACTICES
# ===============================================================================
#
# ✅ DO:
#   • Keep API keys in .env file (never in code)
#   • Use different keys for development/production
#   • Regularly rotate API keys
#   • Monitor API usage and costs
#   • Set up billing alerts
#
# ❌ DON'T:
#   • Commit .env file to version control
#   • Share API keys in screenshots or logs
#   • Use production keys for learning/testing
#   • Leave unused API keys active
#
# ===============================================================================




