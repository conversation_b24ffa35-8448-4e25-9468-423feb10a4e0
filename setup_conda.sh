#!/bin/bash

# ===============================================================================
# 🐍 Vector Database Learning Project - Conda Setup Script (Unix/macOS)
# ===============================================================================

echo "🚀 Vector Database Learning Project - Conda Setup"
echo "=================================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Environment name
ENV_NAME="vector-db-learning"

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️ $1${NC}"
}

# Check if conda is installed
echo "🔍 Checking conda installation..."
if ! command -v conda &> /dev/null; then
    print_error "Conda not found!"
    echo "💡 Please install Anaconda or Miniconda:"
    echo "   • Anaconda: https://www.anaconda.com/download"
    echo "   • Miniconda: https://docs.conda.io/en/latest/miniconda.html"
    exit 1
fi

CONDA_VERSION=$(conda --version)
print_status "Found $CONDA_VERSION"

# Check if environment already exists
echo ""
echo "🏗️ Checking if environment '$ENV_NAME' exists..."
if conda env list | grep -q "^$ENV_NAME "; then
    print_warning "Environment '$ENV_NAME' already exists"
    read -p "🔄 Do you want to update it? (y/n): " -n 1 -r
    echo ""
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "🔄 Updating environment..."
        conda env update -f environment.yml
    else
        echo "📋 Using existing environment"
    fi
else
    echo "🆕 Creating new environment '$ENV_NAME'..."
    conda env create -f environment.yml
fi

if [ $? -eq 0 ]; then
    print_status "Environment setup completed!"
else
    print_error "Environment setup failed!"
    exit 1
fi

# Setup Jupyter kernel
echo ""
echo "📓 Setting up Jupyter kernel..."
conda run -n $ENV_NAME python -m ipykernel install --user --name $ENV_NAME --display-name "Python (vector-db-learning)"

if [ $? -eq 0 ]; then
    print_status "Jupyter kernel installed!"
else
    print_warning "Jupyter kernel setup failed (you can do this manually later)"
fi

# Setup .env file
echo ""
echo "🔑 Setting up environment file..."
if [ ! -f ".env" ]; then
    if [ -f ".env.template" ]; then
        cp .env.template .env
        print_status ".env file created from template"
        print_info "Please edit .env file and add your API keys"
    else
        print_warning ".env.template not found"
    fi
else
    print_status ".env file already exists"
fi

# Print next steps
echo ""
echo "🎉 Setup Complete! Next Steps:"
echo "=============================="
echo ""
echo "1. Activate your conda environment:"
echo "   conda activate $ENV_NAME"
echo ""
echo "2. Edit your .env file with API keys:"
echo "   • HF_TOKEN (HuggingFace - Free)"
echo "   • OPENAI_API_KEY (OpenAI - Paid)"
echo "   • GOOGLE_API_KEY (Google AI - Free tier)"
echo "   • PINECONE_API_KEY (Pinecone - Free tier)"
echo ""
echo "3. Start Jupyter Notebook:"
echo "   jupyter notebook"
echo ""
echo "4. Open the first notebook:"
echo "   01_Vector_Database_Fundamentals.ipynb"
echo ""
echo "📚 Learning Path:"
echo "1. 01_Vector_Database_Fundamentals.ipynb"
echo "2. 02_Embeddings_and_Models.ipynb"
echo "3. 03_FAISS_Implementation.ipynb"
echo "4. 04_Pinecone_Cloud_Database.ipynb"
echo "5. 05_RAG_Implementation.ipynb"
echo "6. 06_Production_Deployment.ipynb"
echo ""
echo "Happy Learning! 🐍🚀"
