#!/usr/bin/env python3
"""
Vector Database Learning Project - Environment Test Script
Tests if all required packages are installed and working correctly
"""

import sys
import importlib
from typing import Dict, List, <PERSON><PERSON>

def print_banner():
    """Print test banner"""
    banner = """
    🧪 Vector Database Learning Project - Environment Test
    =====================================================
    
    This script tests if your environment is set up correctly
    for the vector database learning project.
    
    """
    print(banner)

def test_package_import(package_name: str, import_name: str = None) -> Tuple[bool, str]:
    """Test if a package can be imported"""
    if import_name is None:
        import_name = package_name
    
    try:
        module = importlib.import_module(import_name)
        version = getattr(module, '__version__', 'Unknown')
        return True, version
    except ImportError as e:
        return False, str(e)

def run_package_tests() -> Dict[str, Dict]:
    """Run tests for all required packages"""
    
    # Core packages
    core_packages = {
        'numpy': 'numpy',
        'pandas': 'pandas',
        'matplotlib': 'matplotlib',
        'seaborn': 'seaborn',
        'scikit-learn': 'sklearn',
        'scipy': 'scipy',
        'jupyter': 'jupyter',
        'requests': 'requests',
        'tqdm': 'tqdm',
        'python-dotenv': 'dotenv'
    }
    
    # AI/ML packages
    ai_packages = {
        'langchain': 'langchain',
        'langchain-community': 'langchain_community',
        'langchain-core': 'langchain_core',
        'langchain-huggingface': 'langchain_huggingface',
        'faiss-cpu': 'faiss',
        'sentence-transformers': 'sentence_transformers',
        'transformers': 'transformers',
        'torch': 'torch',
        'huggingface-hub': 'huggingface_hub'
    }
    
    # Optional packages
    optional_packages = {
        'pinecone-client': 'pinecone',
        'chromadb': 'chromadb',
        'openai': 'openai',
        'google-generativeai': 'google.generativeai',
        'langchain-openai': 'langchain_openai',
        'langchain-google-genai': 'langchain_google_genai',
        'langchain-pinecone': 'langchain_pinecone',
        'streamlit': 'streamlit',
        'plotly': 'plotly'
    }
    
    results = {}
    
    # Test core packages
    print("🔧 Testing Core Packages:")
    print("-" * 40)
    core_results = {}
    for package, import_name in core_packages.items():
        success, version = test_package_import(package, import_name)
        core_results[package] = {'success': success, 'version': version}
        
        if success:
            print(f"✅ {package:<20} v{version}")
        else:
            print(f"❌ {package:<20} FAILED: {version}")
    
    results['core'] = core_results
    
    # Test AI/ML packages
    print("\n🤖 Testing AI/ML Packages:")
    print("-" * 40)
    ai_results = {}
    for package, import_name in ai_packages.items():
        success, version = test_package_import(package, import_name)
        ai_results[package] = {'success': success, 'version': version}
        
        if success:
            print(f"✅ {package:<25} v{version}")
        else:
            print(f"❌ {package:<25} FAILED: {version}")
    
    results['ai'] = ai_results
    
    # Test optional packages
    print("\n🌟 Testing Optional Packages:")
    print("-" * 40)
    optional_results = {}
    for package, import_name in optional_packages.items():
        success, version = test_package_import(package, import_name)
        optional_results[package] = {'success': success, 'version': version}
        
        if success:
            print(f"✅ {package:<25} v{version}")
        else:
            print(f"⚠️ {package:<25} Not available (optional)")
    
    results['optional'] = optional_results
    
    return results

def test_basic_functionality():
    """Test basic functionality of key packages"""
    print("\n🧪 Testing Basic Functionality:")
    print("-" * 40)
    
    tests_passed = 0
    total_tests = 0
    
    # Test numpy
    total_tests += 1
    try:
        import numpy as np
        arr = np.array([1, 2, 3])
        assert arr.sum() == 6
        print("✅ NumPy: Array operations working")
        tests_passed += 1
    except Exception as e:
        print(f"❌ NumPy: {e}")
    
    # Test pandas
    total_tests += 1
    try:
        import pandas as pd
        df = pd.DataFrame({'a': [1, 2], 'b': [3, 4]})
        assert len(df) == 2
        print("✅ Pandas: DataFrame operations working")
        tests_passed += 1
    except Exception as e:
        print(f"❌ Pandas: {e}")
    
    # Test matplotlib
    total_tests += 1
    try:
        import matplotlib.pyplot as plt
        fig, ax = plt.subplots()
        plt.close(fig)
        print("✅ Matplotlib: Plotting functionality working")
        tests_passed += 1
    except Exception as e:
        print(f"❌ Matplotlib: {e}")
    
    # Test sentence-transformers (if available)
    total_tests += 1
    try:
        from sentence_transformers import SentenceTransformer
        print("✅ Sentence Transformers: Import successful")
        tests_passed += 1
    except Exception as e:
        print(f"⚠️ Sentence Transformers: {e}")
    
    # Test FAISS (if available)
    total_tests += 1
    try:
        import faiss
        index = faiss.IndexFlatL2(128)
        print(f"✅ FAISS: Index creation working (v{faiss.__version__})")
        tests_passed += 1
    except Exception as e:
        print(f"⚠️ FAISS: {e}")
    
    return tests_passed, total_tests

def check_environment_variables():
    """Check if environment variables are set"""
    print("\n🔑 Checking Environment Variables:")
    print("-" * 40)
    
    import os
    from dotenv import load_dotenv
    
    # Try to load .env file
    env_file_exists = os.path.exists('.env')
    if env_file_exists:
        load_dotenv()
        print("✅ .env file found and loaded")
    else:
        print("⚠️ .env file not found (you can create it from .env.template)")
    
    # Check API keys
    api_keys = {
        'HF_TOKEN': 'HuggingFace Token',
        'OPENAI_API_KEY': 'OpenAI API Key',
        'GOOGLE_API_KEY': 'Google AI API Key',
        'PINECONE_API_KEY': 'Pinecone API Key'
    }
    
    keys_found = 0
    for key, description in api_keys.items():
        value = os.getenv(key)
        if value and value != f"your_{key.lower()}_here":
            print(f"✅ {description}: Set")
            keys_found += 1
        else:
            print(f"⚠️ {description}: Not set")
    
    return keys_found, len(api_keys)

def print_summary(results: Dict, func_tests: Tuple[int, int], env_vars: Tuple[int, int]):
    """Print test summary"""
    print("\n" + "="*60)
    print("📊 TEST SUMMARY")
    print("="*60)
    
    # Package summary
    core_success = sum(1 for pkg in results['core'].values() if pkg['success'])
    core_total = len(results['core'])
    
    ai_success = sum(1 for pkg in results['ai'].values() if pkg['success'])
    ai_total = len(results['ai'])
    
    optional_success = sum(1 for pkg in results['optional'].values() if pkg['success'])
    optional_total = len(results['optional'])
    
    print(f"📦 Core Packages:     {core_success}/{core_total} ({'✅' if core_success == core_total else '⚠️'})")
    print(f"🤖 AI/ML Packages:   {ai_success}/{ai_total} ({'✅' if ai_success >= ai_total * 0.8 else '⚠️'})")
    print(f"🌟 Optional Packages: {optional_success}/{optional_total} ({'✅' if optional_success > 0 else '⚠️'})")
    print(f"🧪 Functionality:    {func_tests[0]}/{func_tests[1]} ({'✅' if func_tests[0] >= func_tests[1] * 0.8 else '⚠️'})")
    print(f"🔑 API Keys:         {env_vars[0]}/{env_vars[1]} ({'✅' if env_vars[0] > 0 else '⚠️'})")
    
    # Overall status
    overall_score = (
        (core_success / core_total) * 0.4 +
        (ai_success / ai_total) * 0.3 +
        (optional_success / optional_total) * 0.1 +
        (func_tests[0] / func_tests[1]) * 0.15 +
        (env_vars[0] / env_vars[1]) * 0.05
    )
    
    print(f"\n🎯 Overall Score: {overall_score:.1%}")
    
    if overall_score >= 0.9:
        print("🎉 EXCELLENT! Your environment is ready for learning!")
    elif overall_score >= 0.7:
        print("✅ GOOD! Your environment should work well for most modules.")
    elif overall_score >= 0.5:
        print("⚠️ PARTIAL! Some features may not work. Consider installing missing packages.")
    else:
        print("❌ ISSUES! Please fix the installation before proceeding.")
    
    # Next steps
    print(f"\n🚀 Next Steps:")
    if core_success < core_total:
        print("   • Install missing core packages")
    if ai_success < ai_total * 0.8:
        print("   • Install missing AI/ML packages")
    if env_vars[0] == 0:
        print("   • Set up API keys in .env file")
    if overall_score >= 0.7:
        print("   • Start with: 01_Vector_Database_Fundamentals.ipynb")

def main():
    """Main test function"""
    print_banner()
    
    print(f"🐍 Python Version: {sys.version}")
    print(f"📍 Python Path: {sys.executable}")
    print("")
    
    # Run package tests
    results = run_package_tests()
    
    # Test basic functionality
    func_tests = test_basic_functionality()
    
    # Check environment variables
    env_vars = check_environment_variables()
    
    # Print summary
    print_summary(results, func_tests, env_vars)

if __name__ == "__main__":
    main()
