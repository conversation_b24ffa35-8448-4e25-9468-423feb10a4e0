{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# 🧠 Embedding Models Comparison - Which AI Brain to Choose?\n",
    "\n",
    "## 🤔 The Problem We're Solving\n",
    "\n",
    "**You learned the basics, but now you're wondering:**\n",
    "- Which AI model creates the best embeddings?\n",
    "- Should I use free models or paid ones?\n",
    "- What's the difference between HuggingFace, OpenAI, and Google?\n",
    "- How do I choose the right model for my project?\n",
    "\n",
    "## 🎯 What You'll Learn (Step by Step)\n",
    "1. **Different AI Companies** and their embedding models\n",
    "2. **Free vs Paid Models** - what you get for your money\n",
    "3. **Performance Comparison** - which models work best for what\n",
    "4. **Practical Testing** - see the differences yourself\n",
    "5. **Decision Framework** - how to choose the right model\n",
    "\n",
    "## 🏆 Real-World Context\n",
    "- **HuggingFace**: Open-source, free, good for learning and small projects\n",
    "- **OpenAI**: Premium quality, costs money, used by ChatGPT\n",
    "- **Google AI**: Free tier available, good balance of quality and cost\n",
    "- **Others**: Cohere, Anthropic, specialized models for specific domains\n",
    "\n",
    "**🎯 Goal**: By the end, you'll know exactly which model to use for your needs!"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 📊 Step 1: Understanding the Embedding Model Landscape\n",
    "\n",
    "### 🏢 The Major Players\n",
    "\n",
    "#### 🤗 **HuggingFace (Open Source)**\n",
    "- **Cost**: FREE\n",
    "- **Quality**: Good to Very Good\n",
    "- **Speed**: Fast (runs locally)\n",
    "- **Best For**: Learning, prototypes, cost-sensitive projects\n",
    "- **Popular Models**: all-MiniLM-L6-v2, all-mpnet-base-v2\n",
    "\n",
    "#### 🌟 **OpenAI (Premium)**\n",
    "- **Cost**: ~$0.0001 per 1K tokens\n",
    "- **Quality**: Excellent\n",
    "- **Speed**: Medium (API calls)\n",
    "- **Best For**: Production apps, highest quality needed\n",
    "- **Popular Models**: text-embedding-ada-002, text-embedding-3-small\n",
    "\n",
    "#### 🎯 **Google AI (Freemium)**\n",
    "- **Cost**: FREE tier (60 requests/minute)\n",
    "- **Quality**: Very Good\n",
    "- **Speed**: Medium (API calls)\n",
    "- **Best For**: Balanced quality and cost\n",
    "- **Popular Models**: embedding-001, textembedding-gecko\n",
    "\n",
    "### 🎯 Which Should You Choose?\n",
    "- **Just Learning**: Start with HuggingFace (free)\n",
    "- **Building a Product**: Consider OpenAI or Google\n",
    "- **Budget Conscious**: Google AI free tier\n",
    "- **Need Best Quality**: OpenAI (but costs money)"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 🛠️ Step 1.1: Setup Our Testing Environment\n",
    "import numpy as np\n",
    "import pandas as pd\n",
    "import matplotlib.pyplot as plt\n",
    "import time\n",
    "from sklearn.metrics.pairwise import cosine_similarity\n",
    "import warnings\n",
    "warnings.filterwarnings('ignore')\n",
    "\n",
    "# Load environment variables (API keys)\n",
    "from dotenv import load_dotenv\n",
    "import os\n",
    "load_dotenv()\n",
    "\n",
    "print(\"✅ Basic setup complete!\")\n",
    "print(\"🎯 What we'll do:\")\n",
    "print(\"   • Test different embedding models\")\n",
    "print(\"   • Compare their quality and speed\")\n",
    "print(\"   • Help you choose the best one\")\n",
    "\n",
    "# Check which API keys are available\n",
    "print(\"\\n🔑 Checking available API keys:\")\n",
    "print(f\"   HuggingFace: {'✅ Available' if os.getenv('HF_TOKEN') else '❌ Not set (still works, but slower)'}\")\n",
    "print(f\"   OpenAI: {'✅ Available' if os.getenv('OPENAI_API_KEY') else '❌ Not set (will skip OpenAI tests)'}\")\n",
    "print(f\"   Google AI: {'✅ Available' if os.getenv('GOOGLE_API_KEY') else '❌ Not set (will skip Google tests)'}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 🤗 Step 2: Testing HuggingFace Models (Free & Local)\n",
    "\n",
    "### 🎯 Why Start with HuggingFace?\n",
    "- **Completely FREE** - no API costs\n",
    "- **Runs locally** - no internet needed after download\n",
    "- **Many options** - thousands of models to choose from\n",
    "- **Good quality** - often 80-90% as good as paid models\n",
    "\n",
    "### 🔬 Models We'll Test:\n",
    "1. **all-MiniLM-L6-v2**: Fast and lightweight (384 dimensions)\n",
    "2. **all-mpnet-base-v2**: Higher quality but slower (768 dimensions)\n",
    "\n",
    "### 💡 What Each Dimension Number Means:\n",
    "- **384 dimensions**: Faster, uses less memory, good for most tasks\n",
    "- **768 dimensions**: More detailed, better quality, uses more resources\n",
    "- **1536+ dimensions**: Premium models, highest quality"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 🚀 Step 2.1: Load HuggingFace Models\n",
    "from langchain_huggingface import HuggingFaceEmbeddings\n",
    "\n",
    "print(\"📥 Loading HuggingFace models...\")\n",
    "print(\"⏳ First time may take a few minutes to download...\")\n",
    "\n",
    "# Model 1: Fast and lightweight\n",
    "print(\"\\n🏃 Loading MiniLM (Fast Model)...\")\n",
    "model_mini = HuggingFaceEmbeddings(\n",
    "    model_name=\"all-MiniLM-L6-v2\",\n",
    "    model_kwargs={'device': 'cpu'},\n",
    "    encode_kwargs={'normalize_embeddings': True}\n",
    ")\n",
    "print(\"✅ MiniLM loaded - 384 dimensions, optimized for speed\")\n",
    "\n",
    "# Model 2: Higher quality\n",
    "print(\"\\n🎯 Loading MPNet (Quality Model)...\")\n",
    "model_mpnet = HuggingFaceEmbeddings(\n",
    "    model_name=\"all-mpnet-base-v2\",\n",
    "    model_kwargs={'device': 'cpu'},\n",
    "    encode_kwargs={'normalize_embeddings': True}\n",
    ")\n",
    "print(\"✅ MPNet loaded - 768 dimensions, optimized for quality\")\n",
    "\n",
    "print(\"\\n🎉 Both HuggingFace models ready!\")\n",
    "print(\"💡 What we have:\")\n",
    "print(\"   • MiniLM: Fast, 384 numbers per text\")\n",
    "print(\"   • MPNet: Better quality, 768 numbers per text\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 🧪 Step 2.2: Test Both Models with Sample Text\n",
    "# Let's see how they perform on the same text\n",
    "\n",
    "test_sentences = [\n",
    "    \"The cat sat on the mat\",\n",
    "    \"A feline rested on the rug\",\n",
    "    \"The dog barked loudly\",\n",
    "    \"Python programming language\",\n",
    "    \"Machine learning algorithms\"\n",
    "]\n",
    "\n",
    "print(\"🧪 Testing both models with sample sentences:\")\n",
    "print(\"\\n📝 Test sentences:\")\n",
    "for i, sentence in enumerate(test_sentences, 1):\n",
    "    print(f\"{i}. {sentence}\")\n",
    "\n",
    "# Test MiniLM model\n",
    "print(\"\\n⏱️ Testing MiniLM speed...\")\n",
    "start_time = time.time()\n",
    "mini_embeddings = [model_mini.embed_query(sentence) for sentence in test_sentences]\n",
    "mini_time = time.time() - start_time\n",
    "print(f\"✅ MiniLM: {mini_time:.2f} seconds for {len(test_sentences)} sentences\")\n",
    "print(f\"📏 Embedding size: {len(mini_embeddings[0])} dimensions\")\n",
    "\n",
    "# Test MPNet model\n",
    "print(\"\\n⏱️ Testing MPNet speed...\")\n",
    "start_time = time.time()\n",
    "mpnet_embeddings = [model_mpnet.embed_query(sentence) for sentence in test_sentences]\n",
    "mpnet_time = time.time() - start_time\n",
    "print(f\"✅ MPNet: {mpnet_time:.2f} seconds for {len(test_sentences)} sentences\")\n",
    "print(f\"📏 Embedding size: {len(mpnet_embeddings[0])} dimensions\")\n",
    "\n",
    "# Compare speeds\n",
    "print(f\"\\n🏃 Speed Comparison:\")\n",
    "print(f\"   MiniLM: {mini_time:.2f}s (faster)\")\n",
    "print(f\"   MPNet:  {mpnet_time:.2f}s ({mpnet_time/mini_time:.1f}x slower)\")\n",
    "print(f\"\\n💡 Trade-off: MiniLM is faster, MPNet might be more accurate\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 🔍 Step 2.3: Compare Quality - Similarity Detection\n",
    "# Let's see which model better understands that \"cat\" and \"feline\" are similar\n",
    "\n",
    "def test_similarity_understanding(model, model_name):\n",
    "    \"\"\"Test how well a model understands semantic similarity\"\"\"\n",
    "    \n",
    "    # Test pairs: first two should be similar, last should be different\n",
    "    test_pairs = [\n",
    "        (\"The cat sat on the mat\", \"A feline rested on the rug\"),  # Should be similar\n",
    "        (\"car\", \"automobile\"),  # Should be similar\n",
    "        (\"dog\", \"computer\")  # Should be different\n",
    "    ]\n",
    "    \n",
    "    print(f\"\\n🔍 Testing {model_name} similarity understanding:\")\n",
    "    \n",
    "    for text1, text2 in test_pairs:\n",
    "        # Get embeddings\n",
    "        emb1 = model.embed_query(text1)\n",
    "        emb2 = model.embed_query(text2)\n",
    "        \n",
    "        # Calculate similarity\n",
    "        similarity = cosine_similarity([emb1], [emb2])[0][0]\n",
    "        \n",
    "        # Interpret result\n",
    "        if similarity > 0.7:\n",
    "            interpretation = \"🟢 Very Similar\"\n",
    "        elif similarity > 0.5:\n",
    "            interpretation = \"🟡 Somewhat Similar\"\n",
    "        else:\n",
    "            interpretation = \"🔴 Different\"\n",
    "        \n",
    "        print(f\"   '{text1}' vs '{text2}'\")\n",
    "        print(f\"   Similarity: {similarity:.3f} {interpretation}\")\n",
    "    \n",
    "    return True\n",
    "\n",
    "# Test both models\n",
    "test_similarity_understanding(model_mini, \"MiniLM\")\n",
    "test_similarity_understanding(model_mpnet, \"MPNet\")\n",
    "\n",
    "print(\"\\n💡 What to look for:\")\n",
    "print(\"   • Both models should rate 'cat/feline' as similar\")\n",
    "print(\"   • Both should rate 'car/automobile' as similar\")\n",
    "print(\"   • Both should rate 'dog/computer' as different\")\n",
    "print(\"   • MPNet might show slightly better understanding\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 🌟 Step 3: Testing Premium Models (OpenAI & Google)\n",
    "\n",
    "### 💰 Why Test Premium Models?\n",
    "- **Higher Quality**: Often 5-15% better than free models\n",
    "- **More Training Data**: Trained on larger, more diverse datasets\n",
    "- **Better Understanding**: Superior grasp of nuanced language\n",
    "- **Production Ready**: Designed for commercial applications\n",
    "\n",
    "### 🤔 The Trade-off:\n",
    "- **Cost**: You pay per API call\n",
    "- **Dependency**: Need internet connection\n",
    "- **Speed**: Network latency vs local processing\n",
    "\n",
    "### 🎯 When Worth the Cost:\n",
    "- Customer-facing applications\n",
    "- High-stakes search (legal, medical)\n",
    "- When accuracy is critical\n",
    "- Production applications with budget"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 🌟 Step 3.1: Test OpenAI Embeddings (if available)\n",
    "openai_available = bool(os.getenv('OPENAI_API_KEY'))\n",
    "\n",
    "if openai_available:\n",
    "    try:\n",
    "        from langchain_openai import OpenAIEmbeddings\n",
    "        \n",
    "        print(\"🌟 Loading OpenAI model...\")\n",
    "        model_openai = OpenAIEmbeddings(\n",
    "            model=\"text-embedding-ada-002\",\n",
    "            openai_api_key=os.getenv('OPENAI_API_KEY')\n",
    "        )\n",
    "        \n",
    "        print(\"✅ OpenAI model loaded!\")\n",
    "        print(\"💎 Model: text-embedding-ada-002\")\n",
    "        print(\"📏 Dimensions: 1536 (highest quality)\")\n",
    "        print(\"💰 Cost: ~$0.0001 per 1K tokens\")\n",
    "        \n",
    "        # Test with our sample\n",
    "        print(\"\\n🧪 Testing OpenAI quality...\")\n",
    "        test_similarity_understanding(model_openai, \"OpenAI\")\n",
    "        \n",
    "    except Exception as e:\n",
    "        print(f\"❌ OpenAI failed: {e}\")\n",
    "        openai_available = False\n",
    "else:\n",
    "    print(\"⚠️ OpenAI API key not found\")\n",
    "    print(\"💡 To test OpenAI:\")\n",
    "    print(\"   1. Get API key from platform.openai.com\")\n",
    "    print(\"   2. Add OPENAI_API_KEY to .env file\")\n",
    "    print(\"   3. Restart notebook\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 🎯 Step 3.2: Test Google AI Embeddings (if available)\n",
    "google_available = bool(os.getenv('GOOGLE_API_KEY'))\n",
    "\n",
    "if google_available:\n",
    "    try:\n",
    "        from langchain_google_genai import GoogleGenerativeAIEmbeddings\n",
    "        \n",
    "        print(\"🎯 Loading Google AI model...\")\n",
    "        model_google = GoogleGenerativeAIEmbeddings(\n",
    "            model=\"models/embedding-001\",\n",
    "            google_api_key=os.getenv('GOOGLE_API_KEY')\n",
    "        )\n",
    "        \n",
    "        print(\"✅ Google AI model loaded!\")\n",
    "        print(\"🎯 Model: embedding-001\")\n",
    "        print(\"📏 Dimensions: 768 (good quality)\")\n",
    "        print(\"🆓 Cost: FREE tier (60 requests/minute)\")\n",
    "        \n",
    "        # Test with our sample\n",
    "        print(\"\\n🧪 Testing Google AI quality...\")\n",
    "        test_similarity_understanding(model_google, \"Google AI\")\n",
    "        \n",
    "    except Exception as e:\n",
    "        print(f\"❌ Google AI failed: {e}\")\n",
    "        google_available = False\n",
    "else:\n",
    "    print(\"⚠️ Google API key not found\")\n",
    "    print(\"💡 To test Google AI:\")\n",
    "    print(\"   1. Get API key from makersuite.google.com\")\n",
    "    print(\"   2. Add GOOGLE_API_KEY to .env file\")\n",
    "    print(\"   3. Restart notebook\")\n",
    "    print(\"   4. Enjoy the FREE tier!\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 📊 Step 4: The Ultimate Comparison\n",
    "\n",
    "### 🏆 Model Comparison Summary\n",
    "\n",
    "| Model | Cost | Quality | Speed | Dimensions | Best For |\n",
    "|-------|------|---------|-------|------------|----------|\n",
    "| **HuggingFace MiniLM** | FREE | Good | Fast | 384 | Learning, prototypes |\n",
    "| **HuggingFace MPNet** | FREE | Very Good | Medium | 768 | Quality on budget |\n",
    "| **OpenAI Ada-002** | $0.0001/1K | Excellent | Medium | 1536 | Production apps |\n",
    "| **Google AI** | FREE tier | Very Good | Medium | 768 | Balanced choice |\n",
    "\n",
    "### 🎯 Decision Framework\n",
    "\n",
    "#### 🆓 **Choose HuggingFace MiniLM if:**\n",
    "- You're just learning\n",
    "- Building a prototype\n",
    "- Speed is critical\n",
    "- Zero budget\n",
    "\n",
    "#### 🎯 **Choose HuggingFace MPNet if:**\n",
    "- You need better quality than MiniLM\n",
    "- Still want to avoid API costs\n",
    "- Building a serious project\n",
    "- Can handle slower processing\n",
    "\n",
    "#### 🆓 **Choose Google AI if:**\n",
    "- You want premium quality for free\n",
    "- Don't mind API dependency\n",
    "- Usage fits within free tier\n",
    "- Want good balance of cost/quality\n",
    "\n",
    "#### 💰 **Choose OpenAI if:**\n",
    "- Building a commercial product\n",
    "- Need the absolute best quality\n",
    "- Have budget for API costs\n",
    "- Quality is more important than cost"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 🎓 Summary: You're Now an Embedding Expert!\n",
    "\n",
    "### 🏆 What You've Learned:\n",
    "1. **Model Landscape**: Understanding of major embedding providers\n",
    "2. **Trade-offs**: Speed vs Quality vs Cost\n",
    "3. **Practical Testing**: How to evaluate models yourself\n",
    "4. **Decision Framework**: How to choose the right model\n",
    "\n",
    "### 💡 Key Insights:\n",
    "- **Free models** (HuggingFace) are often \"good enough\"\n",
    "- **Premium models** (OpenAI) provide 5-15% better quality\n",
    "- **Google AI** offers great middle ground with free tier\n",
    "- **Speed vs Quality** is the main trade-off to consider\n",
    "\n",
    "### 🚀 Next Steps:\n",
    "Now that you know how to choose embedding models, let's learn how to:\n",
    "- **Module 3**: Store and search embeddings efficiently with FAISS\n",
    "- **Module 4**: Scale to millions of vectors with Pinecone\n",
    "- **Module 5**: Build complete RAG systems\n",
    "\n",
    "### 🎯 Pro Tips for Your Projects:\n",
    "1. **Start with free models** for prototyping\n",
    "2. **Upgrade to premium** only when quality matters\n",
    "3. **Test with your actual data** - results may vary\n",
    "4. **Consider total cost** including development time\n",
    "\n",
    "**Ready for Module 3? Let's build lightning-fast search with FAISS!** 🚀"
   ]
  },
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.8.5"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 4
}
