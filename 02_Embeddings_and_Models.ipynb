# 🛠️ Setup and Imports
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.decomposition import PCA
import time
import warnings
warnings.filterwarnings('ignore')

# Load environment variables
from dotenv import load_dotenv
import os
load_dotenv()

# Set up API keys
os.environ['HF_TOKEN'] = os.getenv("HF_TOKEN", "")
os.environ['OPENAI_API_KEY'] = os.getenv("OPENAI_API_KEY", "")
os.environ['GOOGLE_API_KEY'] = os.getenv("GOOGLE_API_KEY", "")

print("✅ Environment setup complete!")
print("📦 Ready to explore different embedding models")

# 🚀 HuggingFace Embeddings
from langchain_huggingface import HuggingFaceEmbeddings

print("📥 Loading HuggingFace embedding models...")

# Different HuggingFace models to compare
hf_models = {
    'MiniLM': {
        'model': HuggingFaceEmbeddings(
            model_name="all-MiniLM-L6-v2",
            model_kwargs={'device': 'cpu'},
            encode_kwargs={'normalize_embeddings': True}
        ),
        'description': 'Fast, lightweight, 384 dimensions',
        'use_case': 'General purpose, speed-critical applications'
    },
    'MPNet': {
        'model': HuggingFaceEmbeddings(
            model_name="all-mpnet-base-v2",
            model_kwargs={'device': 'cpu'},
            encode_kwargs={'normalize_embeddings': True}
        ),
        'description': 'Higher quality, 768 dimensions',
        'use_case': 'Better accuracy, semantic search'
    }
}

print("✅ HuggingFace models loaded!")
for name, info in hf_models.items():
    print(f"🤖 {name}: {info['description']}")

# 🌟 OpenAI Embeddings (if API key available)
openai_available = bool(os.getenv('OPENAI_API_KEY'))

if openai_available:
    try:
        from langchain_openai import OpenAIEmbeddings
        
        openai_model = OpenAIEmbeddings(
            model="text-embedding-ada-002",
            openai_api_key=os.getenv('OPENAI_API_KEY')
        )
        
        print("✅ OpenAI embeddings loaded!")
        print("🌟 Model: text-embedding-ada-002 (1536 dimensions)")
        print("💡 Use case: High-quality general purpose embeddings")
        
    except Exception as e:
        print(f"❌ OpenAI embeddings failed to load: {e}")
        openai_available = False
else:
    print("⚠️ OpenAI API key not found - skipping OpenAI embeddings")
    print("💡 Add OPENAI_API_KEY to .env file to test OpenAI embeddings")

# 🎯 Google AI Embeddings (if API key available)
google_available = bool(os.getenv('GOOGLE_API_KEY'))

if google_available:
    try:
        from langchain_google_genai import GoogleGenerativeAIEmbeddings
        
        google_model = GoogleGenerativeAIEmbeddings(
            model="models/embedding-001",
            google_api_key=os.getenv('GOOGLE_API_KEY')
        )
        
        print("✅ Google AI embeddings loaded!")
        print("🎯 Model: embedding-001 (768 dimensions)")
        print("💡 Use case: Google's latest embedding technology")
        
    except Exception as e:
        print(f"❌ Google AI embeddings failed to load: {e}")
        google_available = False
else:
    print("⚠️ Google API key not found - skipping Google AI embeddings")
    print("💡 Add GOOGLE_API_KEY to .env file to test Google AI embeddings")

# 📝 Test Documents for Comparison
test_documents = [
    "The quick brown fox jumps over the lazy dog.",
    "Machine learning is a subset of artificial intelligence.",
    "Python is a versatile programming language.",
    "The weather today is sunny and warm.",
    "Vector databases enable semantic search capabilities."
]

test_query = "artificial intelligence and machine learning"

print("📚 Test Documents:")
for i, doc in enumerate(test_documents, 1):
    print(f"{i}. {doc}")
    
print(f"\n🔍 Test Query: '{test_query}'")